<?php

namespace App\Models;

use App\Services\CreditTransactionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'credit_package_id',
        'type',
        'credit_amount',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'payment_status',
        'description',
        'metadata',
        'processed_at',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // When a transaction is being created, apply business rules
        static::creating(function (CreditTransaction $transaction) {
            $transaction->applyBusinessRules();
        });

        // When a transaction is created
        static::created(function (CreditTransaction $transaction) {
            $transaction->updateUserBalance();
        });

        // When a transaction is updated (e.g., status changed from pending to completed)
        static::updated(function (CreditTransaction $transaction) {
            // Only update balance if payment_status changed to completed
            if ($transaction->wasChanged('payment_status') && $transaction->payment_status === 'completed') {
                $transaction->updateUserBalance();
            }
        });

        // When a transaction is deleted
        static::deleted(function (CreditTransaction $transaction) {
            if ($transaction->payment_status === 'completed') {
                // Reverse the credit amount
                $transaction->user->decrement('credit_balance', $transaction->credit_amount);
            }
        });
    }

    protected function casts(): array
    {
        return [
            'amount_paid' => 'decimal:2',
            'metadata' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the credit package associated with the transaction
     */
    public function creditPackage()
    {
        return $this->belongsTo(CreditPackage::class);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', 'failed');
    }

    /**
     * Scope for credit transactions (positive amounts)
     */
    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * Scope for debit transactions (negative amounts)
     */
    public function scopeDebits($query)
    {
        return $query->where('credit_amount', '<', 0);
    }

    /**
     * Get formatted amount paid
     */
    public function getFormattedAmountPaidAttribute()
    {
        return $this->amount_paid ? 'RM ' . number_format($this->amount_paid, 2) : null;
    }

    /**
     * Check if transaction is a credit (positive amount)
     */
    public function getIsCreditAttribute()
    {
        return $this->credit_amount > 0;
    }

    /**
     * Check if transaction is a debit (negative amount)
     */
    public function getIsDebitAttribute()
    {
        return $this->credit_amount < 0;
    }

    /**
     * Update user's credit balance based on this transaction
     */
    protected function updateUserBalance(): void
    {
        // Only update balance for completed transactions
        if ($this->payment_status !== 'completed') {
            return;
        }

        // Update user's credit balance
        $this->user->increment('credit_balance', $this->credit_amount);

        // Log the balance update
        \Log::info('Credit balance updated via transaction', [
            'transaction_id' => $this->id,
            'user_id' => $this->user_id,
            'credit_amount' => $this->credit_amount,
            'new_balance' => $this->user->fresh()->credit_balance,
            'transaction_type' => $this->type,
            'payment_method' => $this->payment_method,
        ]);
    }

    /**
     * Apply business rules based on transaction type
     */
    protected function applyBusinessRules(): void
    {
        if (!$this->type) {
            return;
        }

        $service = new CreditTransactionService();

        try {
            // Process the transaction data using the service
            $data = $this->toArray();
            $processedData = $service->processTransactionData($data);

            // Apply the processed data to the model
            foreach ($processedData as $key => $value) {
                if ($this->isFillable($key)) {
                    $this->$key = $value;
                }
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to apply business rules to transaction', [
                'transaction_type' => $this->type,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get transaction type configuration
     */
    public function getTypeConfig(): array
    {
        $service = new CreditTransactionService();
        return $service->getTransactionTypeConfig($this->type);
    }

    /**
     * Check if transaction type requires payment amount
     */
    public function requiresPaymentAmount(): bool
    {
        $config = $this->getTypeConfig();
        return $config['requires_payment_amount'] ?? false;
    }

    /**
     * Check if transaction type requires payment method
     */
    public function requiresPaymentMethod(): bool
    {
        $config = $this->getTypeConfig();
        return $config['requires_payment_method'] ?? false;
    }

    /**
     * Check if transaction type allows package selection
     */
    public function allowsPackageSelection(): bool
    {
        $config = $this->getTypeConfig();
        return $config['allows_package_selection'] ?? false;
    }

    /**
     * Get expected credit amount sign for this transaction type
     */
    public function getExpectedCreditAmountSign(): string
    {
        $config = $this->getTypeConfig();
        return $config['credit_amount_sign'] ?? 'flexible';
    }

    /**
     * Validate transaction against business rules
     */
    public function validateBusinessRules(): array
    {
        $errors = [];

        // Check credit amount sign
        $expectedSign = $this->getExpectedCreditAmountSign();

        switch ($expectedSign) {
            case 'positive':
                if ($this->credit_amount <= 0) {
                    $errors[] = "Credit amount must be positive for {$this->type} transactions.";
                }
                break;
            case 'negative':
                if ($this->credit_amount >= 0) {
                    $errors[] = "Credit amount must be negative for {$this->type} transactions.";
                }
                break;
        }

        // Check required fields
        if ($this->requiresPaymentAmount() && empty($this->amount_paid)) {
            $errors[] = "Payment amount is required for {$this->type} transactions.";
        }

        if ($this->requiresPaymentMethod() && empty($this->payment_method)) {
            $errors[] = "Payment method is required for {$this->type} transactions.";
        }

        // Check user has sufficient credits for negative transactions
        if ($this->credit_amount < 0 && $this->user) {
            $requiredAmount = abs($this->credit_amount);
            if ($this->user->credit_balance < $requiredAmount) {
                $errors[] = "Insufficient credits. User has {$this->user->credit_balance} credits, but {$requiredAmount} required.";
            }
        }

        return $errors;
    }
}
