<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'credit_package_id',
        'type',
        'credit_amount',
        'amount_paid',
        'payment_method',
        'payment_reference',
        'payment_status',
        'description',
        'metadata',
        'processed_at',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted(): void
    {
        // When a transaction is created
        static::created(function (CreditTransaction $transaction) {
            $transaction->updateUserBalance();
        });

        // When a transaction is updated (e.g., status changed from pending to completed)
        static::updated(function (CreditTransaction $transaction) {
            // Only update balance if payment_status changed to completed
            if ($transaction->wasChanged('payment_status') && $transaction->payment_status === 'completed') {
                $transaction->updateUserBalance();
            }
        });

        // When a transaction is deleted
        static::deleted(function (CreditTransaction $transaction) {
            if ($transaction->payment_status === 'completed') {
                // Reverse the credit amount
                $transaction->user->decrement('credit_balance', $transaction->credit_amount);
            }
        });
    }

    protected function casts(): array
    {
        return [
            'amount_paid' => 'decimal:2',
            'metadata' => 'array',
            'processed_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the transaction
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the credit package associated with the transaction
     */
    public function creditPackage()
    {
        return $this->belongsTo(CreditPackage::class);
    }

    /**
     * Scope for completed transactions
     */
    public function scopeCompleted($query)
    {
        return $query->where('payment_status', 'completed');
    }

    /**
     * Scope for pending transactions
     */
    public function scopePending($query)
    {
        return $query->where('payment_status', 'pending');
    }

    /**
     * Scope for failed transactions
     */
    public function scopeFailed($query)
    {
        return $query->where('payment_status', 'failed');
    }

    /**
     * Scope for credit transactions (positive amounts)
     */
    public function scopeCredits($query)
    {
        return $query->where('credit_amount', '>', 0);
    }

    /**
     * Scope for debit transactions (negative amounts)
     */
    public function scopeDebits($query)
    {
        return $query->where('credit_amount', '<', 0);
    }

    /**
     * Get formatted amount paid
     */
    public function getFormattedAmountPaidAttribute()
    {
        return $this->amount_paid ? 'RM ' . number_format($this->amount_paid, 2) : null;
    }

    /**
     * Check if transaction is a credit (positive amount)
     */
    public function getIsCreditAttribute()
    {
        return $this->credit_amount > 0;
    }

    /**
     * Check if transaction is a debit (negative amount)
     */
    public function getIsDebitAttribute()
    {
        return $this->credit_amount < 0;
    }

    /**
     * Update user's credit balance based on this transaction
     */
    protected function updateUserBalance(): void
    {
        // Only update balance for completed transactions
        if ($this->payment_status !== 'completed') {
            return;
        }

        // Update user's credit balance
        $this->user->increment('credit_balance', $this->credit_amount);

        // Log the balance update
        \Log::info('Credit balance updated via transaction', [
            'transaction_id' => $this->id,
            'user_id' => $this->user_id,
            'credit_amount' => $this->credit_amount,
            'new_balance' => $this->user->fresh()->credit_balance,
            'transaction_type' => $this->type,
            'payment_method' => $this->payment_method,
        ]);
    }
}
