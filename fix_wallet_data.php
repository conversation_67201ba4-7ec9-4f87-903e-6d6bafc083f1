<?php

require_once 'bootstrap/app.php';

use Illuminate\Support\Facades\DB;

echo "Fixing wallet transaction data...\n";

// Check current types
$types = DB::table('wallet_transactions')->select('type')->distinct()->get();
echo "Current transaction types:\n";
foreach ($types as $type) {
    echo "- {$type->type}\n";
}

// Update transaction types
$typeMapping = [
    'purchase' => 'top_up',
    'usage' => 'payment',
    'refund' => 'refund',
    'bonus' => 'bonus',
    'adjustment' => 'adjustment'
];

foreach ($typeMapping as $oldType => $newType) {
    $count = DB::table('wallet_transactions')
        ->where('type', $oldType)
        ->update(['type' => $newType]);
    
    if ($count > 0) {
        echo "Updated {$count} transactions from '{$oldType}' to '{$newType}'\n";
    }
}

// Check types after update
$types = DB::table('wallet_transactions')->select('type')->distinct()->get();
echo "\nTransaction types after update:\n";
foreach ($types as $type) {
    echo "- {$type->type}\n";
}

echo "\nData fix completed!\n";
