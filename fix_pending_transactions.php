<?php

echo "=== FIXING PENDING CREDIT TRANSACTIONS ===\n\n";

// Change to backend directory
chdir('backend');

// Create script to fix pending transactions
$fixScript = '
use App\Models\User;
use App\Models\CreditTransaction;

echo "🔍 <NAME_EMAIL> and pending transactions...\n\n";

// Find <EMAIL> user
$admin = User::where("email", "<EMAIL>")->first();

if (!$admin) {
    echo "❌ <EMAIL> not found. Available users:\n";
    $users = User::select("email", "credit_balance")->get();
    foreach ($users as $user) {
        echo "  - {$user->email} (Balance: {$user->credit_balance})\n";
    }
    exit;
}

echo "✅ Found <EMAIL> (ID: {$admin->id})\n";
echo "Current balance: {$admin->credit_balance}\n\n";

// Get all transactions for this user
$transactions = $admin->creditTransactions()->orderBy("created_at", "desc")->get();
echo "📊 Total transactions: " . $transactions->count() . "\n\n";

if ($transactions->count() > 0) {
    echo "Transaction Analysis:\n";
    $pendingTransactions = [];
    $completedTransactions = [];
    
    foreach ($transactions as $transaction) {
        echo "- ID: {$transaction->id}\n";
        echo "  Type: {$transaction->type}\n";
        echo "  Amount: {$transaction->credit_amount}\n";
        echo "  Status: {$transaction->payment_status}\n";
        echo "  Description: {$transaction->description}\n";
        echo "  Created: {$transaction->created_at}\n";
        echo "  Processed: " . ($transaction->processed_at ? $transaction->processed_at : "Not processed") . "\n";
        echo "  ---\n";
        
        if ($transaction->payment_status === "pending") {
            $pendingTransactions[] = $transaction;
        } else {
            $completedTransactions[] = $transaction;
        }
    }
    
    echo "\n📈 Summary:\n";
    echo "Pending transactions: " . count($pendingTransactions) . "\n";
    echo "Completed transactions: " . count($completedTransactions) . "\n";
    
    if (count($pendingTransactions) > 0) {
        echo "\n🔧 Fixing pending transactions...\n";
        
        foreach ($pendingTransactions as $transaction) {
            echo "Fixing transaction ID: {$transaction->id}\n";
            
            // Update transaction to completed
            $transaction->update([
                "payment_status" => "completed",
                "processed_at" => now(),
            ]);
            
            // Add credits to user balance
            $admin->increment("credit_balance", $transaction->credit_amount);
            
            echo "  ✅ Added {$transaction->credit_amount} credits to balance\n";
        }
        
        $admin->refresh();
        echo "\n🎉 Fixed! New balance: {$admin->credit_balance}\n";
    }
    
    // Verify the balance calculation
    echo "\n🧮 Balance Verification:\n";
    $totalPurchased = $admin->creditTransactions()
        ->where("type", "purchase")
        ->where("payment_status", "completed")
        ->sum("credit_amount");
    
    $totalUsed = abs($admin->creditTransactions()
        ->where("type", "usage")
        ->sum("credit_amount"));
    
    $totalCredits = $admin->creditTransactions()
        ->where("type", "credit")
        ->where("payment_status", "completed")
        ->sum("credit_amount");
    
    echo "Total purchased (completed): {$totalPurchased}\n";
    echo "Total used: {$totalUsed}\n";
    echo "Total credits: {$totalCredits}\n";
    echo "Expected balance: " . ($totalPurchased + $totalCredits - $totalUsed) . "\n";
    echo "Actual balance: {$admin->credit_balance}\n";
    
    if (($totalPurchased + $totalCredits - $totalUsed) == $admin->credit_balance) {
        echo "✅ Balance is now correct!\n";
    } else {
        echo "❌ Balance still incorrect - manual intervention needed\n";
    }
    
} else {
    echo "❌ No transactions <NAME_EMAIL>\n";
}

echo "\n✅ Transaction fix complete!\n";
';

// Write the fix script
file_put_contents('temp_fix_pending.php', "<?php\n" . $fixScript);

// Execute the fix
echo "Executing pending transaction fix...\n\n";
$output = shell_exec('php artisan tinker < temp_fix_pending.php 2>&1');

// Display output
if ($output) {
    echo $output;
} else {
    echo "No output received from tinker command\n";
}

// Clean up
if (file_exists('temp_fix_pending.php')) {
    unlink('temp_fix_pending.php');
}

echo "\n=== PENDING TRANSACTION FIX COMPLETE ===\n";
