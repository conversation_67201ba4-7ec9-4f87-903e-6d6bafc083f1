<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Console\Command;

class FixCreditBalanceNow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:fix-now {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix credit balance immediately for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔧 FIXING CREDIT BALANCE FOR: {$email}");
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("❌ User not found: {$email}");
            return;
        }
        
        $this->info("✅ Found user: {$user->name} (ID: {$user->id})");
        $this->info("📊 Current balance: {$user->credit_balance}");
        
        // Calculate correct balance from all completed transactions
        $purchaseCredits = $user->creditTransactions()
            ->where('type', 'purchase')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;
            
        $bonusCredits = $user->creditTransactions()
            ->where('type', 'bonus')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;
            
        $adjustmentCredits = $user->creditTransactions()
            ->where('type', 'adjustment')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;
            
        $creditCredits = $user->creditTransactions()
            ->where('type', 'credit')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;
            
        $usageCredits = $user->creditTransactions()
            ->where('type', 'usage')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0; // This should be negative
            
        $refundCredits = $user->creditTransactions()
            ->where('type', 'refund')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;
        
        $correctBalance = $purchaseCredits + $bonusCredits + $adjustmentCredits + $creditCredits + $usageCredits + $refundCredits;
        
        $this->info("💰 Balance breakdown:");
        $this->line("  Purchase credits: {$purchaseCredits}");
        $this->line("  Bonus credits:    {$bonusCredits}");
        $this->line("  Adjustment:       {$adjustmentCredits}");
        $this->line("  Credit:           {$creditCredits}");
        $this->line("  Usage:            {$usageCredits}");
        $this->line("  Refund:           {$refundCredits}");
        $this->line("  ─────────────────────────");
        $this->line("  Correct total:    {$correctBalance}");
        
        if ($user->credit_balance != $correctBalance) {
            $this->warn("❌ Balance mismatch detected!");
            $this->info("Current: {$user->credit_balance}");
            $this->info("Should be: {$correctBalance}");
            $this->info("Difference: " . ($correctBalance - $user->credit_balance));
            
            $this->info("🔧 Updating balance...");
            $user->update(['credit_balance' => $correctBalance]);
            $user->refresh();
            
            $this->info("✅ Balance updated successfully!");
            $this->info("New balance: {$user->credit_balance}");
            
            if ($user->credit_balance == $correctBalance) {
                $this->info("🎉 Balance is now correct!");
            } else {
                $this->error("❌ Something went wrong with the update");
            }
        } else {
            $this->info("✅ Balance is already correct!");
        }
        
        $this->info("🎯 Fix complete!");
    }
}
