<?php

namespace App\Services;

use App\Models\User;
use App\Models\CreditTransaction;
use App\Models\CreditPackage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class CreditTransactionService
{
    /**
     * Transaction type constants
     */
    const TYPE_PURCHASE = 'purchase';
    const TYPE_USAGE = 'usage';
    const TYPE_REFUND = 'refund';
    const TYPE_BONUS = 'bonus';
    const TYPE_ADJUSTMENT = 'adjustment';

    /**
     * Valid transaction types
     */
    const VALID_TYPES = [
        self::TYPE_PURCHASE,
        self::TYPE_USAGE,
        self::TYPE_REFUND,
        self::TYPE_BONUS,
        self::TYPE_ADJUSTMENT,
    ];

    /**
     * Create a new credit transaction with proper business logic
     */
    public function createTransaction(array $data): CreditTransaction
    {
        // Validate transaction type
        if (!in_array($data['type'], self::VALID_TYPES)) {
            throw ValidationException::withMessages([
                'type' => 'Invalid transaction type provided.',
            ]);
        }

        // Apply type-specific business logic
        $processedData = $this->processTransactionData($data);

        // Validate the processed data
        $this->validateTransactionData($processedData);

        // Create transaction in database transaction
        return DB::transaction(function () use ($processedData) {
            $transaction = CreditTransaction::create($processedData);
            
            Log::info('Credit transaction created', [
                'transaction_id' => $transaction->id,
                'type' => $transaction->type,
                'user_id' => $transaction->user_id,
                'credit_amount' => $transaction->credit_amount,
                'payment_status' => $transaction->payment_status,
            ]);

            return $transaction;
        });
    }

    /**
     * Process transaction data based on type
     */
    public function processTransactionData(array $data): array
    {
        $type = $data['type'];
        
        switch ($type) {
            case self::TYPE_PURCHASE:
                return $this->processPurchaseTransaction($data);
            
            case self::TYPE_USAGE:
                return $this->processUsageTransaction($data);
            
            case self::TYPE_REFUND:
                return $this->processRefundTransaction($data);
            
            case self::TYPE_BONUS:
                return $this->processBonusTransaction($data);
            
            case self::TYPE_ADJUSTMENT:
                return $this->processAdjustmentTransaction($data);
            
            default:
                throw ValidationException::withMessages([
                    'type' => 'Unsupported transaction type.',
                ]);
        }
    }

    /**
     * Process purchase transaction
     */
    protected function processPurchaseTransaction(array $data): array
    {
        // Ensure credit amount is positive for purchases
        $data['credit_amount'] = abs($data['credit_amount']);
        
        // Set default payment method if not provided
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $creditAmount = $data['credit_amount'];
            $data['description'] = "Credit purchase: {$creditAmount} credits";
        }
        
        // Purchase transactions start as pending by default unless specified
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'pending';
        }
        
        return $data;
    }

    /**
     * Process usage transaction
     */
    protected function processUsageTransaction(array $data): array
    {
        // Ensure credit amount is negative for usage
        $data['credit_amount'] = -abs($data['credit_amount']);
        
        // Usage transactions don't have payment amounts
        $data['amount_paid'] = null;
        $data['payment_method'] = 'system';
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $creditAmount = abs($data['credit_amount']);
            $data['description'] = "Credit usage: {$creditAmount} credits";
        }
        
        // Usage transactions are immediately completed
        $data['payment_status'] = 'completed';
        $data['processed_at'] = now();
        
        return $data;
    }

    /**
     * Process refund transaction
     */
    protected function processRefundTransaction(array $data): array
    {
        // Ensure credit amount is positive for refunds (adding credits back)
        $data['credit_amount'] = abs($data['credit_amount']);
        
        // Set payment method to manual for refunds
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $creditAmount = $data['credit_amount'];
            $data['description'] = "Credit refund: {$creditAmount} credits";
        }
        
        // Refunds are typically completed immediately
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'completed';
            $data['processed_at'] = now();
        }
        
        return $data;
    }

    /**
     * Process bonus transaction
     */
    protected function processBonusTransaction(array $data): array
    {
        // Ensure credit amount is positive for bonuses
        $data['credit_amount'] = abs($data['credit_amount']);
        
        // Bonus transactions don't have payment amounts
        $data['amount_paid'] = null;
        $data['payment_method'] = 'system';
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $creditAmount = $data['credit_amount'];
            $data['description'] = "Bonus credits: {$creditAmount} credits";
        }
        
        // Bonus transactions are immediately completed
        $data['payment_status'] = 'completed';
        $data['processed_at'] = now();
        
        return $data;
    }

    /**
     * Process adjustment transaction
     */
    protected function processAdjustmentTransaction(array $data): array
    {
        // Adjustments can be positive or negative - keep original sign
        // No automatic sign change for adjustments
        
        // Adjustment transactions don't typically have payment amounts
        if (!isset($data['amount_paid'])) {
            $data['amount_paid'] = null;
        }
        
        // Set payment method to manual for adjustments
        if (empty($data['payment_method'])) {
            $data['payment_method'] = 'manual';
        }
        
        // Set default description if not provided
        if (empty($data['description'])) {
            $creditAmount = $data['credit_amount'];
            $adjustmentType = $creditAmount > 0 ? 'positive' : 'negative';
            $data['description'] = "Credit adjustment ({$adjustmentType}): {$creditAmount} credits";
        }
        
        // Adjustments are typically completed immediately
        if (empty($data['payment_status'])) {
            $data['payment_status'] = 'completed';
            $data['processed_at'] = now();
        }
        
        return $data;
    }

    /**
     * Validate transaction data based on type
     */
    public function validateTransactionData(array $data): void
    {
        $type = $data['type'];

        // Common validations
        if (empty($data['user_id'])) {
            throw ValidationException::withMessages([
                'user_id' => 'User ID is required.',
            ]);
        }

        if (!User::find($data['user_id'])) {
            throw ValidationException::withMessages([
                'user_id' => 'User not found.',
            ]);
        }

        if (empty($data['credit_amount']) || !is_numeric($data['credit_amount'])) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Valid credit amount is required.',
            ]);
        }

        // Type-specific validations
        switch ($type) {
            case self::TYPE_PURCHASE:
                $this->validatePurchaseTransaction($data);
                break;

            case self::TYPE_USAGE:
                $this->validateUsageTransaction($data);
                break;

            case self::TYPE_REFUND:
                $this->validateRefundTransaction($data);
                break;

            case self::TYPE_BONUS:
                $this->validateBonusTransaction($data);
                break;

            case self::TYPE_ADJUSTMENT:
                $this->validateAdjustmentTransaction($data);
                break;
        }
    }

    /**
     * Validate purchase transaction
     */
    protected function validatePurchaseTransaction(array $data): void
    {
        if ($data['credit_amount'] <= 0) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Purchase transactions must have positive credit amount.',
            ]);
        }

        // If credit package is specified, validate it
        if (!empty($data['credit_package_id'])) {
            $package = CreditPackage::find($data['credit_package_id']);
            if (!$package) {
                throw ValidationException::withMessages([
                    'credit_package_id' => 'Credit package not found.',
                ]);
            }

            if (!$package->is_active) {
                throw ValidationException::withMessages([
                    'credit_package_id' => 'Credit package is not active.',
                ]);
            }
        }
    }

    /**
     * Validate usage transaction
     */
    protected function validateUsageTransaction(array $data): void
    {
        if ($data['credit_amount'] >= 0) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Usage transactions must have negative credit amount.',
            ]);
        }

        // Check if user has sufficient credits for usage
        $user = User::find($data['user_id']);
        $usageAmount = abs($data['credit_amount']);

        if ($user->credit_balance < $usageAmount) {
            throw ValidationException::withMessages([
                'credit_amount' => "Insufficient credits. User has {$user->credit_balance} credits, but {$usageAmount} required.",
            ]);
        }
    }

    /**
     * Validate refund transaction
     */
    protected function validateRefundTransaction(array $data): void
    {
        if ($data['credit_amount'] <= 0) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Refund transactions must have positive credit amount.',
            ]);
        }
    }

    /**
     * Validate bonus transaction
     */
    protected function validateBonusTransaction(array $data): void
    {
        if ($data['credit_amount'] <= 0) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Bonus transactions must have positive credit amount.',
            ]);
        }
    }

    /**
     * Validate adjustment transaction
     */
    protected function validateAdjustmentTransaction(array $data): void
    {
        if ($data['credit_amount'] == 0) {
            throw ValidationException::withMessages([
                'credit_amount' => 'Adjustment transactions cannot have zero credit amount.',
            ]);
        }

        // For negative adjustments, check if user has sufficient credits
        if ($data['credit_amount'] < 0) {
            $user = User::find($data['user_id']);
            $adjustmentAmount = abs($data['credit_amount']);

            if ($user->credit_balance < $adjustmentAmount) {
                throw ValidationException::withMessages([
                    'credit_amount' => "Insufficient credits for negative adjustment. User has {$user->credit_balance} credits, but {$adjustmentAmount} would be deducted.",
                ]);
            }
        }
    }

    /**
     * Get transaction type configuration
     */
    public function getTransactionTypeConfig(string $type): array
    {
        switch ($type) {
            case self::TYPE_PURCHASE:
                return [
                    'label' => 'Purchase',
                    'description' => 'Credit purchase transaction',
                    'credit_amount_sign' => 'positive',
                    'requires_payment_amount' => true,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'pending',
                    'allows_package_selection' => true,
                ];

            case self::TYPE_USAGE:
                return [
                    'label' => 'Usage',
                    'description' => 'Credit usage transaction',
                    'credit_amount_sign' => 'negative',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => false,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_REFUND:
                return [
                    'label' => 'Refund',
                    'description' => 'Credit refund transaction',
                    'credit_amount_sign' => 'positive',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_BONUS:
                return [
                    'label' => 'Bonus',
                    'description' => 'Bonus credit transaction',
                    'credit_amount_sign' => 'positive',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => false,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            case self::TYPE_ADJUSTMENT:
                return [
                    'label' => 'Adjustment',
                    'description' => 'Credit adjustment transaction',
                    'credit_amount_sign' => 'flexible',
                    'requires_payment_amount' => false,
                    'requires_payment_method' => true,
                    'default_payment_status' => 'completed',
                    'allows_package_selection' => false,
                ];

            default:
                return [];
        }
    }

    /**
     * Get all transaction type configurations
     */
    public function getAllTransactionTypeConfigs(): array
    {
        $configs = [];
        foreach (self::VALID_TYPES as $type) {
            $configs[$type] = $this->getTransactionTypeConfig($type);
        }
        return $configs;
    }

    /**
     * Calculate expected credit balance for a user
     */
    public function calculateExpectedBalance(User $user): int
    {
        $transactions = $user->creditTransactions()
            ->where('payment_status', 'completed')
            ->get();

        $balance = 0;

        foreach ($transactions as $transaction) {
            $balance += $transaction->credit_amount;
        }

        return $balance;
    }

    /**
     * Verify and fix user credit balance
     */
    public function verifyAndFixBalance(User $user): array
    {
        $currentBalance = $user->credit_balance;
        $expectedBalance = $this->calculateExpectedBalance($user);

        $result = [
            'user_id' => $user->id,
            'current_balance' => $currentBalance,
            'expected_balance' => $expectedBalance,
            'balance_correct' => $currentBalance == $expectedBalance,
            'fixed' => false,
        ];

        if ($currentBalance != $expectedBalance) {
            $user->update(['credit_balance' => $expectedBalance]);
            $result['fixed'] = true;

            Log::info('Credit balance corrected', [
                'user_id' => $user->id,
                'old_balance' => $currentBalance,
                'new_balance' => $expectedBalance,
                'difference' => $expectedBalance - $currentBalance,
            ]);
        }

        return $result;
    }

    /**
     * Get detailed balance breakdown for a user
     */
    public function getBalanceBreakdown(User $user): array
    {
        $breakdown = [];

        foreach (self::VALID_TYPES as $type) {
            $total = $user->creditTransactions()
                ->where('type', $type)
                ->where('payment_status', 'completed')
                ->sum('credit_amount') ?? 0;

            $count = $user->creditTransactions()
                ->where('type', $type)
                ->where('payment_status', 'completed')
                ->count();

            $breakdown[$type] = [
                'total_credits' => $total,
                'transaction_count' => $count,
            ];
        }

        return $breakdown;
    }
}
