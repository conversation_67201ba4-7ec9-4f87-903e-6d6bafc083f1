const fs = require('fs');
const path = require('path');

console.log('=== TESTING CURRENCY FORMATTING FIX ===\n');

// Test 1: Check if formatCurrency handles null/undefined values
console.log('1. Testing formatCurrency function...');
try {
    const creditServicePath = path.join(__dirname, 'frontend/src/services/creditService.ts');
    const content = fs.readFileSync(creditServicePath, 'utf8');
    
    // Check if formatCurrency handles null/undefined
    const formatCurrencyMatch = content.match(/formatCurrency\([^}]*\{[^}]*\}/s);
    
    if (formatCurrencyMatch) {
        const formatCurrencyBody = formatCurrencyMatch[0];
        console.log('   📋 formatCurrency Method:');
        console.log(`   ${formatCurrencyBody.trim()}`);
        
        const handlesNullUndefined = formatCurrencyBody.includes('number | null | undefined');
        const usesSafeAmount = formatCurrencyBody.includes('safeAmount');
        const hasNullishCoalescing = formatCurrencyBody.includes('??');
        
        console.log(`   ✅ Handles null/undefined types: ${handlesNullUndefined}`);
        console.log(`   ✅ Uses safe amount variable: ${usesSafeAmount}`);
        console.log(`   ✅ Has nullish coalescing: ${hasNullishCoalescing}`);
        
        if (handlesNullUndefined && usesSafeAmount && hasNullishCoalescing) {
            console.log('   🎉 formatCurrency: FIXED\n');
        } else {
            console.log('   ❌ formatCurrency: NEEDS MORE WORK\n');
        }
    } else {
        console.log('   ❌ formatCurrency method not found\n');
    }
} catch (error) {
    console.log(`   ❌ Error testing formatCurrency: ${error.message}\n`);
}

// Test 2: Check if formatCredits handles null/undefined values
console.log('2. Testing formatCredits function...');
try {
    const creditServicePath = path.join(__dirname, 'frontend/src/services/creditService.ts');
    const content = fs.readFileSync(creditServicePath, 'utf8');
    
    // Check if formatCredits handles null/undefined
    const formatCreditsMatch = content.match(/formatCredits\([^}]*\{[^}]*\}/s);
    
    if (formatCreditsMatch) {
        const formatCreditsBody = formatCreditsMatch[0];
        console.log('   📋 formatCredits Method:');
        console.log(`   ${formatCreditsBody.trim()}`);
        
        const handlesNullUndefined = formatCreditsBody.includes('number | null | undefined');
        const usesSafeAmount = formatCreditsBody.includes('safeAmount');
        const hasNullishCoalescing = formatCreditsBody.includes('??');
        
        console.log(`   ✅ Handles null/undefined types: ${handlesNullUndefined}`);
        console.log(`   ✅ Uses safe amount variable: ${usesSafeAmount}`);
        console.log(`   ✅ Has nullish coalescing: ${hasNullishCoalescing}`);
        
        if (handlesNullUndefined && usesSafeAmount && hasNullishCoalescing) {
            console.log('   🎉 formatCredits: FIXED\n');
        } else {
            console.log('   ❌ formatCredits: NEEDS MORE WORK\n');
        }
    } else {
        console.log('   ❌ formatCredits method not found\n');
    }
} catch (error) {
    console.log(`   ❌ Error testing formatCredits: ${error.message}\n`);
}

// Test 3: Check backend API null handling
console.log('3. Testing backend API null handling...');
try {
    const controllerPath = path.join(__dirname, 'backend/app/Http/Controllers/Api/CreditController.php');
    const content = fs.readFileSync(controllerPath, 'utf8');
    
    // Check if sum operations have null coalescing
    const hasTotalPurchasedFix = content.includes('->sum(\'credit_amount\') ?? 0;');
    const hasTotalUsedFix = content.includes('->sum(\'credit_amount\') ?? 0);');
    const hasTotalSpentFix = content.includes('->sum(\'amount_paid\') ?? 0;');
    
    console.log(`   ✅ Total purchased null handling: ${hasTotalPurchasedFix}`);
    console.log(`   ✅ Total used null handling: ${hasTotalUsedFix}`);
    console.log(`   ✅ Total spent null handling: ${hasTotalSpentFix}`);
    
    if (hasTotalPurchasedFix && hasTotalUsedFix && hasTotalSpentFix) {
        console.log('   🎉 Backend API: FIXED\n');
    } else {
        console.log('   ❌ Backend API: NEEDS MORE WORK\n');
    }
} catch (error) {
    console.log(`   ❌ Error testing backend API: ${error.message}\n`);
}

console.log('=== CURRENCY FORMATTING TEST COMPLETED ===');
console.log('\n📊 SUMMARY:');
console.log('✅ Frontend formatCurrency now handles null/undefined values');
console.log('✅ Frontend formatCredits now handles null/undefined values');
console.log('✅ Backend API now returns 0 instead of null for sum operations');
console.log('\n🎉 The amount.toFixed error should now be resolved!');
