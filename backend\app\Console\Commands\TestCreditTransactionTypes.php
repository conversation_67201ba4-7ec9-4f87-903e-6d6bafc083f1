<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\CreditTransactionService;
use Illuminate\Console\Command;

class TestCreditTransactionTypes extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'credit:test-transaction-types {--user-email=<EMAIL>}';

    /**
     * The console command description.
     */
    protected $description = 'Test all 5 credit transaction types to ensure they work correctly';

    protected CreditTransactionService $transactionService;

    public function __construct(CreditTransactionService $transactionService)
    {
        parent::__construct();
        $this->transactionService = $transactionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userEmail = $this->option('user-email');
        $user = User::where('email', $userEmail)->first();

        if (!$user) {
            $this->error("User with email {$userEmail} not found.");
            return 1;
        }

        $this->info("Testing credit transaction types for user: {$user->name} ({$user->email})");
        $this->info("Initial balance: {$user->credit_balance} credits");
        $this->newLine();

        $initialBalance = $user->credit_balance;
        $testResults = [];

        // Test 1: Purchase Transaction
        $this->info("🛒 Testing PURCHASE transaction...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'purchase',
                'credit_amount' => 100,
                'amount_paid' => 10.00,
                'payment_method' => 'manual',
                'payment_status' => 'completed',
                'description' => 'Test purchase transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Purchase transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be positive)");
            $this->info("   Payment status: {$transaction->payment_status}");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['purchase'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Purchase transaction failed: " . $e->getMessage());
            $testResults['purchase'] = false;
        }
        $this->newLine();

        // Test 2: Usage Transaction
        $this->info("📱 Testing USAGE transaction...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'usage',
                'credit_amount' => 50,
                'description' => 'Test usage transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Usage transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be negative)");
            $this->info("   Payment method: {$transaction->payment_method} (should be system)");
            $this->info("   Payment status: {$transaction->payment_status} (should be completed)");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['usage'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Usage transaction failed: " . $e->getMessage());
            $testResults['usage'] = false;
        }
        $this->newLine();

        // Test 3: Refund Transaction
        $this->info("💸 Testing REFUND transaction...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'refund',
                'credit_amount' => 25,
                'description' => 'Test refund transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Refund transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be positive)");
            $this->info("   Payment method: {$transaction->payment_method} (should be manual)");
            $this->info("   Payment status: {$transaction->payment_status} (should be completed)");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['refund'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Refund transaction failed: " . $e->getMessage());
            $testResults['refund'] = false;
        }
        $this->newLine();

        // Test 4: Bonus Transaction
        $this->info("🎁 Testing BONUS transaction...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'bonus',
                'credit_amount' => 75,
                'description' => 'Test bonus transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Bonus transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be positive)");
            $this->info("   Payment method: {$transaction->payment_method} (should be system)");
            $this->info("   Payment status: {$transaction->payment_status} (should be completed)");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['bonus'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Bonus transaction failed: " . $e->getMessage());
            $testResults['bonus'] = false;
        }
        $this->newLine();

        // Test 5: Adjustment Transaction (Positive)
        $this->info("⚖️ Testing ADJUSTMENT transaction (positive)...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'adjustment',
                'credit_amount' => 30,
                'description' => 'Test positive adjustment transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Positive adjustment transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be positive)");
            $this->info("   Payment method: {$transaction->payment_method} (should be manual)");
            $this->info("   Payment status: {$transaction->payment_status} (should be completed)");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['adjustment_positive'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Positive adjustment transaction failed: " . $e->getMessage());
            $testResults['adjustment_positive'] = false;
        }
        $this->newLine();

        // Test 6: Adjustment Transaction (Negative)
        $this->info("⚖️ Testing ADJUSTMENT transaction (negative)...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'adjustment',
                'credit_amount' => -20,
                'description' => 'Test negative adjustment transaction',
            ]);
            
            $user->refresh();
            $this->info("✅ Negative adjustment transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount} (should be negative)");
            $this->info("   Payment method: {$transaction->payment_method} (should be manual)");
            $this->info("   Payment status: {$transaction->payment_status} (should be completed)");
            $this->info("   New balance: {$user->credit_balance} credits");
            $testResults['adjustment_negative'] = true;
        } catch (\Exception $e) {
            $this->error("❌ Negative adjustment transaction failed: " . $e->getMessage());
            $testResults['adjustment_negative'] = false;
        }
        $this->newLine();

        // Test 7: Balance Verification
        $this->info("🧮 Testing BALANCE VERIFICATION...");
        $expectedBalance = $this->transactionService->calculateExpectedBalance($user);
        $actualBalance = $user->credit_balance;
        
        if ($expectedBalance == $actualBalance) {
            $this->info("✅ Balance verification passed");
            $this->info("   Expected: {$expectedBalance} credits");
            $this->info("   Actual: {$actualBalance} credits");
            $testResults['balance_verification'] = true;
        } else {
            $this->error("❌ Balance verification failed");
            $this->error("   Expected: {$expectedBalance} credits");
            $this->error("   Actual: {$actualBalance} credits");
            $testResults['balance_verification'] = false;
        }
        $this->newLine();

        // Summary
        $this->info("📊 TEST SUMMARY:");
        $this->line(str_repeat("=", 50));
        
        $passedTests = 0;
        $totalTests = count($testResults);
        
        foreach ($testResults as $test => $passed) {
            $status = $passed ? "✅ PASS" : "❌ FAIL";
            $this->line(sprintf("%-25s: %s", ucfirst(str_replace('_', ' ', $test)), $status));
            if ($passed) $passedTests++;
        }
        
        $this->newLine();
        $this->info("Final balance: {$user->credit_balance} credits (started with {$initialBalance})");
        $this->info("Balance change: " . ($user->credit_balance - $initialBalance) . " credits");
        
        if ($passedTests == $totalTests) {
            $this->info("🎉 ALL TESTS PASSED! ({$passedTests}/{$totalTests})");
            return 0;
        } else {
            $this->error("❌ SOME TESTS FAILED ({$passedTests}/{$totalTests} passed)");
            return 1;
        }
    }
}
