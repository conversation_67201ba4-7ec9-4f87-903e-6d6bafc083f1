<?php

namespace App\Filament\Resources\CreditTransactionResource\Pages;

use App\Filament\Resources\CreditTransactionResource;
use App\Services\CreditTransactionService;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateCreditTransaction extends CreateRecord
{
    protected static string $resource = CreditTransactionResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        try {
            // Use the service to process the transaction data
            $service = new CreditTransactionService();
            $processedData = $service->processTransactionData($data);

            // Validate the processed data
            $service->validateTransactionData($processedData);

            return $processedData;
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Re-throw validation exceptions to be handled by Filament
            throw $e;
        } catch (\Exception $e) {
            // Handle other exceptions
            Notification::make()
                ->title('Transaction Creation Failed')
                ->body($e->getMessage())
                ->danger()
                ->send();

            throw $e;
        }
    }

    protected function afterCreate(): void
    {
        $transaction = $this->record;

        Notification::make()
            ->title('Transaction Created Successfully')
            ->body("Created {$transaction->type} transaction for {$transaction->credit_amount} credits")
            ->success()
            ->send();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->record]);
    }
}
