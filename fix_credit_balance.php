<?php

echo "=== CREDIT BALANCE FIX SCRIPT ===\n\n";

// Change to backend directory
chdir('backend');

// Create a script to fix credit balances
$fixScript = '
use App\Models\User;
use App\Models\CreditTransaction;

echo "🔍 Analyzing credit balance issues...\n\n";

// Get all users with transactions
$usersWithTransactions = User::whereHas("creditTransactions")->get();

echo "Found " . $usersWithTransactions->count() . " users with credit transactions\n\n";

foreach ($usersWithTransactions as $user) {
    echo "👤 User: {$user->email} (ID: {$user->id})\n";
    echo "   Current balance: {$user->credit_balance}\n";
    
    // Calculate expected balance from transactions
    $transactions = $user->creditTransactions;
    
    $purchased = $transactions->where("type", "purchase")->sum("credit_amount");
    $used = abs($transactions->where("type", "usage")->sum("credit_amount"));
    $credits = $transactions->where("type", "credit")->sum("credit_amount");
    $adjustments = $transactions->where("type", "adjustment")->sum("credit_amount");
    $refunds = $transactions->where("type", "refund")->sum("credit_amount");
    
    $expectedBalance = $purchased + $credits + $adjustments + $refunds - $used;
    
    echo "   Transaction summary:\n";
    echo "     Purchased: {$purchased}\n";
    echo "     Used: {$used}\n";
    echo "     Credits: {$credits}\n";
    echo "     Adjustments: {$adjustments}\n";
    echo "     Refunds: {$refunds}\n";
    echo "   Expected balance: {$expectedBalance}\n";
    
    if ($user->credit_balance != $expectedBalance) {
        echo "   ❌ MISMATCH! Fixing balance...\n";
        $user->update(["credit_balance" => $expectedBalance]);
        echo "   ✅ Fixed! New balance: {$expectedBalance}\n";
    } else {
        echo "   ✅ Balance is correct\n";
    }
    echo "\n";
}

// Special <NAME_EMAIL>
echo "🔍 Special <NAME_EMAIL>...\n";
$admin = User::where("email", "<EMAIL>")->first();

if ($admin) {
    echo "✅ Found <EMAIL>\n";
    echo "Current balance: {$admin->credit_balance}\n";
    
    $transactions = $admin->creditTransactions()->orderBy("created_at", "desc")->get();
    echo "Transactions count: " . $transactions->count() . "\n";
    
    if ($transactions->count() > 0) {
        echo "Recent transactions:\n";
        foreach ($transactions->take(5) as $transaction) {
            echo "  - {$transaction->type}: {$transaction->credit_amount} ({$transaction->description})\n";
        }
    }
} else {
    echo "❌ <EMAIL> not found\n";
    echo "Available users:\n";
    $allUsers = User::select("email", "credit_balance")->get();
    foreach ($allUsers as $user) {
        echo "  - {$user->email} (Balance: {$user->credit_balance})\n";
    }
}

echo "\n✅ Credit balance fix complete!\n";
';

// Write the fix script
file_put_contents('temp_fix_credits.php', "<?php\n" . $fixScript);

// Execute the fix
echo "Executing credit balance fix...\n\n";
$output = shell_exec('php artisan tinker < temp_fix_credits.php 2>&1');

// Display output, handling potential null
if ($output) {
    echo $output;
} else {
    echo "No output received from tinker command\n";
}

// Clean up
if (file_exists('temp_fix_credits.php')) {
    unlink('temp_fix_credits.php');
}

echo "\n=== FIX SCRIPT COMPLETE ===\n";
