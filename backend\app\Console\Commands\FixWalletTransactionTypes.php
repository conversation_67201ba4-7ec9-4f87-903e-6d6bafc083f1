<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixWalletTransactionTypes extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'wallet:fix-transaction-types';

    /**
     * The console command description.
     */
    protected $description = 'Fix wallet transaction types for migration';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fixing wallet transaction types...');

        // Check current types
        $types = DB::table('wallet_transactions')->select('type')->distinct()->get();
        $this->info('Current transaction types:');
        foreach ($types as $type) {
            $this->line("- {$type->type}");
        }

        // Update transaction types
        $typeMapping = [
            'purchase' => 'top_up',
            'usage' => 'payment',
            'refund' => 'refund',
            'bonus' => 'bonus',
            'adjustment' => 'adjustment'
        ];

        foreach ($typeMapping as $oldType => $newType) {
            $count = DB::table('wallet_transactions')
                ->where('type', $oldType)
                ->update(['type' => $newType]);
            
            if ($count > 0) {
                $this->info("Updated {$count} transactions from '{$oldType}' to '{$newType}'");
            }
        }

        // Check for any remaining invalid types
        $validTypes = ['top_up', 'payment', 'withdrawal', 'refund', 'bonus', 'adjustment'];
        $invalidTypes = DB::table('wallet_transactions')
            ->whereNotIn('type', $validTypes)
            ->select('type')
            ->distinct()
            ->get();

        if ($invalidTypes->count() > 0) {
            $this->warn('Found invalid transaction types:');
            foreach ($invalidTypes as $type) {
                $this->line("- {$type->type}");
                
                // Set invalid types to 'adjustment' as fallback
                $count = DB::table('wallet_transactions')
                    ->where('type', $type->type)
                    ->update(['type' => 'adjustment']);
                
                $this->info("Updated {$count} invalid '{$type->type}' transactions to 'adjustment'");
            }
        }

        // Check types after update
        $types = DB::table('wallet_transactions')->select('type')->distinct()->get();
        $this->info('Transaction types after update:');
        foreach ($types as $type) {
            $this->line("- {$type->type}");
        }

        $this->info('Data fix completed!');
        return 0;
    }
}
