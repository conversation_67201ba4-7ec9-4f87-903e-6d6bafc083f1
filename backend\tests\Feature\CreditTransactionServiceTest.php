<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\CreditPackage;
use App\Models\CreditTransaction;
use App\Services\CreditTransactionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use Tests\TestCase;

class CreditTransactionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CreditTransactionService $service;
    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CreditTransactionService();
        $this->user = User::factory()->create(['credit_balance' => 1000]);
    }

    /** @test */
    public function it_creates_purchase_transaction_correctly()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'amount_paid' => 50.00,
            'description' => 'Test purchase',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('purchase', $transaction->type);
        $this->assertEquals(500, $transaction->credit_amount); // Should be positive
        $this->assertEquals(50.00, $transaction->amount_paid);
        $this->assertEquals('manual', $transaction->payment_method);
        $this->assertEquals('pending', $transaction->payment_status);
    }

    /** @test */
    public function it_creates_usage_transaction_correctly()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'usage',
            'credit_amount' => 200, // Will be converted to negative
            'description' => 'Test usage',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('usage', $transaction->type);
        $this->assertEquals(-200, $transaction->credit_amount); // Should be negative
        $this->assertNull($transaction->amount_paid);
        $this->assertEquals('system', $transaction->payment_method);
        $this->assertEquals('completed', $transaction->payment_status);
        $this->assertNotNull($transaction->processed_at);
    }

    /** @test */
    public function it_creates_refund_transaction_correctly()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'refund',
            'credit_amount' => 300,
            'description' => 'Test refund',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('refund', $transaction->type);
        $this->assertEquals(300, $transaction->credit_amount); // Should be positive
        $this->assertEquals('manual', $transaction->payment_method);
        $this->assertEquals('completed', $transaction->payment_status);
        $this->assertNotNull($transaction->processed_at);
    }

    /** @test */
    public function it_creates_bonus_transaction_correctly()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'credit_amount' => 100,
            'description' => 'Test bonus',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('bonus', $transaction->type);
        $this->assertEquals(100, $transaction->credit_amount); // Should be positive
        $this->assertNull($transaction->amount_paid);
        $this->assertEquals('system', $transaction->payment_method);
        $this->assertEquals('completed', $transaction->payment_status);
        $this->assertNotNull($transaction->processed_at);
    }

    /** @test */
    public function it_creates_adjustment_transaction_correctly()
    {
        // Positive adjustment
        $data = [
            'user_id' => $this->user->id,
            'type' => 'adjustment',
            'credit_amount' => 150,
            'description' => 'Test positive adjustment',
        ];

        $transaction = $this->service->createTransaction($data);

        $this->assertEquals('adjustment', $transaction->type);
        $this->assertEquals(150, $transaction->credit_amount); // Should keep original sign
        $this->assertEquals('manual', $transaction->payment_method);
        $this->assertEquals('completed', $transaction->payment_status);

        // Negative adjustment
        $data['credit_amount'] = -50;
        $data['description'] = 'Test negative adjustment';

        $transaction = $this->service->createTransaction($data);
        $this->assertEquals(-50, $transaction->credit_amount); // Should keep negative sign
    }

    /** @test */
    public function it_validates_insufficient_credits_for_usage()
    {
        $this->user->update(['credit_balance' => 100]);

        $data = [
            'user_id' => $this->user->id,
            'type' => 'usage',
            'credit_amount' => 200, // More than available
        ];

        $this->expectException(ValidationException::class);
        $this->service->createTransaction($data);
    }

    /** @test */
    public function it_validates_insufficient_credits_for_negative_adjustment()
    {
        $this->user->update(['credit_balance' => 50]);

        $data = [
            'user_id' => $this->user->id,
            'type' => 'adjustment',
            'credit_amount' => -100, // More than available
        ];

        $this->expectException(ValidationException::class);
        $this->service->createTransaction($data);
    }

    /** @test */
    public function it_calculates_expected_balance_correctly()
    {
        // Create various transaction types
        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'usage',
            'credit_amount' => -200,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'bonus',
            'credit_amount' => 100,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'adjustment',
            'credit_amount' => -50,
            'payment_status' => 'completed',
        ]);

        $expectedBalance = $this->service->calculateExpectedBalance($this->user);
        
        // 1000 (initial) + 500 (purchase) - 200 (usage) + 100 (bonus) - 50 (adjustment) = 1350
        $this->assertEquals(1350, $expectedBalance);
    }

    /** @test */
    public function it_verifies_and_fixes_balance()
    {
        // Set incorrect balance
        $this->user->update(['credit_balance' => 500]);

        // Create a completed transaction
        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'purchase',
            'credit_amount' => 300,
            'payment_status' => 'completed',
        ]);

        $result = $this->service->verifyAndFixBalance($this->user);

        $this->assertFalse($result['balance_correct']);
        $this->assertTrue($result['fixed']);
        $this->assertEquals(500, $result['current_balance']);
        $this->assertEquals(800, $result['expected_balance']); // 500 + 300

        // Verify the balance was actually fixed
        $this->user->refresh();
        $this->assertEquals(800, $this->user->credit_balance);
    }

    /** @test */
    public function it_gets_balance_breakdown_correctly()
    {
        // Create transactions of different types
        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'purchase',
            'credit_amount' => 300,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->user->id,
            'type' => 'usage',
            'credit_amount' => -200,
            'payment_status' => 'completed',
        ]);

        $breakdown = $this->service->getBalanceBreakdown($this->user);

        $this->assertEquals(800, $breakdown['purchase']['total_credits']);
        $this->assertEquals(2, $breakdown['purchase']['transaction_count']);
        $this->assertEquals(-200, $breakdown['usage']['total_credits']);
        $this->assertEquals(1, $breakdown['usage']['transaction_count']);
        $this->assertEquals(0, $breakdown['bonus']['total_credits']);
        $this->assertEquals(0, $breakdown['bonus']['transaction_count']);
    }

    /** @test */
    public function it_validates_invalid_transaction_type()
    {
        $data = [
            'user_id' => $this->user->id,
            'type' => 'invalid_type',
            'credit_amount' => 100,
        ];

        $this->expectException(ValidationException::class);
        $this->service->createTransaction($data);
    }

    /** @test */
    public function it_validates_missing_user_id()
    {
        $data = [
            'type' => 'purchase',
            'credit_amount' => 100,
        ];

        $this->expectException(ValidationException::class);
        $this->service->createTransaction($data);
    }
}
