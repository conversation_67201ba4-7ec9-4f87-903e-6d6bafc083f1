<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\CreditTransaction;
use App\Models\CreditPackage;
use Illuminate\Console\Command;

class InvestigateCreditPurchaseIssue extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:investigate-purchase-issue';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Investigate credit purchase issues - find users with 500 credit purchases showing 0 balance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info("🔍 INVESTIGATING CREDIT PURCHASE ISSUES");
        $this->line("=" . str_repeat("=", 60));
        
        // Step 1: Find users with 500 credit transactions but 0 balance
        $this->info("1. Searching for users with 500 credit purchases but 0 balance...");
        
        $problematicUsers = User::whereHas('creditTransactions', function($query) {
            $query->where('credit_amount', 500);
        })->where('credit_balance', 0)->get();
        
        if ($problematicUsers->count() > 0) {
            $this->info("Found {$problematicUsers->count()} users with potential issues:");
            foreach ($problematicUsers as $user) {
                $this->line("  - {$user->email} (ID: {$user->id}) - Balance: {$user->credit_balance}");
            }
        } else {
            $this->info("No users found with 500 credit transactions and 0 balance.");
        }
        
        // Step 2: Find all users with 500 credit transactions
        $this->info("\n2. Finding all users with 500 credit transactions...");
        
        $usersWithTransaction = User::whereHas('creditTransactions', function($query) {
            $query->where('credit_amount', 500);
        })->with(['creditTransactions' => function($query) {
            $query->where('credit_amount', 500)->orderBy('created_at', 'desc');
        }])->get();
        
        if ($usersWithTransaction->count() > 0) {
            $this->info("Found {$usersWithTransaction->count()} users with 500 credit transactions:");
            
            foreach ($usersWithTransaction as $user) {
                $this->line("\n👤 User: {$user->email} (ID: {$user->id})");
                $this->line("   Current Balance: {$user->credit_balance}");
                
                $transactions500 = $user->creditTransactions->where('credit_amount', 500);
                $this->line("   500-credit transactions: " . $transactions500->count());
                
                foreach ($transactions500 as $transaction) {
                    $this->line("   - Transaction ID: {$transaction->id}");
                    $this->line("     Type: {$transaction->type}");
                    $this->line("     Amount: {$transaction->credit_amount}");
                    $this->line("     Status: {$transaction->payment_status}");
                    $this->line("     Method: {$transaction->payment_method}");
                    $this->line("     Created: {$transaction->created_at}");
                    $this->line("     Processed: " . ($transaction->processed_at ?? 'Not processed'));
                    $this->line("     Description: {$transaction->description}");
                    
                    // Check if this transaction should have updated balance
                    if ($transaction->payment_status === 'completed' && $user->credit_balance == 0) {
                        $this->error("     ❌ ISSUE: Completed transaction but user has 0 balance!");
                    }
                    $this->line("");
                }
            }
        } else {
            $this->info("No users found with 500 credit transactions.");
        }
        
        // Step 3: Check for any pending 500 credit transactions
        $this->info("\n3. Checking for pending 500 credit transactions...");
        
        $pendingTransactions = CreditTransaction::where('credit_amount', 500)
            ->where('payment_status', 'pending')
            ->with('user')
            ->get();
            
        if ($pendingTransactions->count() > 0) {
            $this->warn("Found {$pendingTransactions->count()} pending 500-credit transactions:");
            
            foreach ($pendingTransactions as $transaction) {
                $this->line("  - User: {$transaction->user->email}");
                $this->line("    Transaction ID: {$transaction->id}");
                $this->line("    Created: {$transaction->created_at}");
                $this->line("    Payment Method: {$transaction->payment_method}");
                $this->line("    Reference: {$transaction->payment_reference}");
                $this->line("");
            }
        } else {
            $this->info("No pending 500-credit transactions found.");
        }
        
        // Step 4: Check credit packages for 500 credits
        $this->info("\n4. Checking available credit packages...");
        
        $packages500 = CreditPackage::where('credit_amount', 500)->get();
        
        if ($packages500->count() > 0) {
            $this->info("Found {$packages500->count()} packages with 500 credits:");
            foreach ($packages500 as $package) {
                $this->line("  - {$package->name}: RM{$package->price} for {$package->credit_amount} credits");
                $this->line("    Active: " . ($package->is_active ? 'Yes' : 'No'));
            }
        } else {
            $this->info("No credit packages with 500 credits found.");
        }
        
        // Step 5: Summary and recommendations
        $this->info("\n5. SUMMARY AND RECOMMENDATIONS:");
        $this->line(str_repeat("-", 50));
        
        if ($problematicUsers->count() > 0) {
            $this->error("❌ ISSUES FOUND:");
            $this->line("- {$problematicUsers->count()} users have 500 credit transactions but 0 balance");
            $this->line("- This suggests payment processing issues");
            
            $this->info("\n🔧 RECOMMENDED ACTIONS:");
            $this->line("1. Check payment callback processing");
            $this->line("2. Verify transaction status updates");
            $this->line("3. Test payment method functionality");
            $this->line("4. Fix balance calculations for affected users");
            
            if ($this->confirm('Would you like to fix the balances for affected users?')) {
                $this->fixAffectedUsers($problematicUsers);
            }
        } else {
            $this->info("✅ No obvious issues found with 500 credit purchases");
        }
        
        $this->info("\n🎯 INVESTIGATION COMPLETE");
    }
    
    private function fixAffectedUsers($users)
    {
        $this->info("\n🔧 FIXING AFFECTED USERS...");
        
        foreach ($users as $user) {
            $this->line("\nFixing user: {$user->email}");
            
            // Recalculate balance from all completed transactions
            $correctBalance = $user->creditTransactions()
                ->where('payment_status', 'completed')
                ->sum('credit_amount');
                
            $this->line("Current balance: {$user->credit_balance}");
            $this->line("Calculated balance: {$correctBalance}");
            
            if ($correctBalance != $user->credit_balance) {
                $user->update(['credit_balance' => $correctBalance]);
                $this->info("✅ Updated balance to {$correctBalance}");
            } else {
                $this->info("✅ Balance is already correct");
            }
        }
    }
}
