{"ast": null, "code": "import api from './api';\nclass CreditService {\n  // Get user's credit balance\n  async getBalance() {\n    const response = await api.get('/credit/balance');\n    return response.data;\n  }\n\n  // Get available credit packages\n  async getPackages() {\n    const response = await api.get('/credit/packages');\n    return response.data.packages;\n  }\n\n  // Get user's credit transaction history\n  async getTransactions(page = 1) {\n    const response = await api.get(`/credit/transactions?page=${page}`);\n    return response.data;\n  }\n\n  // Get credit statistics\n  async getStatistics() {\n    const response = await api.get('/credit/statistics');\n    return response.data;\n  }\n\n  // Create payment for credit package\n  async createPayment(packageId, redirectUrl) {\n    const response = await api.post('/payment/create', {\n      package_id: packageId,\n      redirect_url: redirectUrl\n    });\n    return response.data;\n  }\n\n  // Check payment status\n  async checkPaymentStatus(transactionId) {\n    const response = await api.get('/payment/status', {\n      params: {\n        transaction_id: transactionId\n      }\n    });\n    return response.data;\n  }\n\n  // Get payment configuration\n  async getPaymentConfig() {\n    const response = await api.get('/payment/config');\n    return response.data;\n  }\n\n  // Format credit amount for display\n  formatCredits(amount) {\n    const safeAmount = amount !== null && amount !== void 0 ? amount : 0;\n    return `${safeAmount.toLocaleString()} credits`;\n  }\n\n  // Format currency for display\n  formatCurrency(amount) {\n    const safeAmount = amount !== null && amount !== void 0 ? amount : 0;\n    return `RM ${safeAmount.toFixed(2)}`;\n  }\n\n  // Get transaction type color\n  getTransactionTypeColor(type) {\n    switch (type) {\n      case 'purchase':\n        return 'success';\n      case 'usage':\n        return 'warning';\n      case 'refund':\n        return 'error';\n      case 'bonus':\n        return 'info';\n      default:\n        return 'default';\n    }\n  }\n\n  // Get payment status color\n  getPaymentStatusColor(status) {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n}\nexport default new CreditService();", "map": {"version": 3, "names": ["api", "CreditService", "getBalance", "response", "get", "data", "getPackages", "packages", "getTransactions", "page", "getStatistics", "createPayment", "packageId", "redirectUrl", "post", "package_id", "redirect_url", "checkPaymentStatus", "transactionId", "params", "transaction_id", "getPaymentConfig", "formatCredits", "amount", "safeAmount", "toLocaleString", "formatCurrency", "toFixed", "getTransactionTypeColor", "type", "getPaymentStatusColor", "status"], "sources": ["C:/laragon/www/frontend/src/services/creditService.ts"], "sourcesContent": ["import api, { endpoints } from './api';\n\nexport interface CreditPackage {\n  id: number;\n  name: string;\n  description: string;\n  price: number;\n  formatted_price: string;\n  credit_amount: number;\n  price_per_credit: number;\n  features: string[];\n}\n\nexport interface CreditTransaction {\n  id: number;\n  type: string;\n  credit_amount: number;\n  amount_paid: number | null;\n  formatted_amount_paid: string | null;\n  payment_method: string | null;\n  payment_status: string;\n  description: string;\n  package_name: string | null;\n  is_credit: boolean;\n  is_debit: boolean;\n  processed_at: string | null;\n  created_at: string;\n}\n\nexport interface CreditBalance {\n  credit_balance: number;\n  user_id: number;\n}\n\nexport interface CreditStatistics {\n  current_balance: number;\n  total_purchased: number;\n  total_used: number;\n  total_spent: number;\n  recent_transactions: Array<{\n    id: number;\n    type: string;\n    credit_amount: number;\n    description: string;\n    created_at: string;\n  }>;\n}\n\nexport interface PaymentResponse {\n  success: boolean;\n  payment_url?: string;\n  bill_id?: string;\n  transaction_id?: number;\n  error?: string;\n}\n\nexport interface PaymentStatus {\n  transaction_id: number;\n  payment_status: string;\n  credit_amount: number;\n  amount_paid: number;\n  processed_at: string | null;\n}\n\nexport interface PaymentConfig {\n  billplz_enabled: boolean;\n  billplz_configured: boolean;\n}\n\nclass CreditService {\n  // Get user's credit balance\n  async getBalance(): Promise<CreditBalance> {\n    const response = await api.get('/credit/balance');\n    return response.data;\n  }\n\n  // Get available credit packages\n  async getPackages(): Promise<CreditPackage[]> {\n    const response = await api.get('/credit/packages');\n    return response.data.packages;\n  }\n\n  // Get user's credit transaction history\n  async getTransactions(page: number = 1): Promise<{\n    transactions: {\n      data: CreditTransaction[];\n      current_page: number;\n      last_page: number;\n      per_page: number;\n      total: number;\n    };\n  }> {\n    const response = await api.get(`/credit/transactions?page=${page}`);\n    return response.data;\n  }\n\n  // Get credit statistics\n  async getStatistics(): Promise<CreditStatistics> {\n    const response = await api.get('/credit/statistics');\n    return response.data;\n  }\n\n  // Create payment for credit package\n  async createPayment(packageId: number, redirectUrl?: string): Promise<PaymentResponse> {\n    const response = await api.post('/payment/create', {\n      package_id: packageId,\n      redirect_url: redirectUrl,\n    });\n    return response.data;\n  }\n\n  // Check payment status\n  async checkPaymentStatus(transactionId: number): Promise<PaymentStatus> {\n    const response = await api.get('/payment/status', {\n      params: { transaction_id: transactionId },\n    });\n    return response.data;\n  }\n\n  // Get payment configuration\n  async getPaymentConfig(): Promise<PaymentConfig> {\n    const response = await api.get('/payment/config');\n    return response.data;\n  }\n\n  // Format credit amount for display\n  formatCredits(amount: number | null | undefined): string {\n    const safeAmount = amount ?? 0;\n    return `${safeAmount.toLocaleString()} credits`;\n  }\n\n  // Format currency for display\n  formatCurrency(amount: number | null | undefined): string {\n    const safeAmount = amount ?? 0;\n    return `RM ${safeAmount.toFixed(2)}`;\n  }\n\n  // Get transaction type color\n  getTransactionTypeColor(type: string): 'success' | 'warning' | 'error' | 'info' | 'default' {\n    switch (type) {\n      case 'purchase':\n        return 'success';\n      case 'usage':\n        return 'warning';\n      case 'refund':\n        return 'error';\n      case 'bonus':\n        return 'info';\n      default:\n        return 'default';\n    }\n  }\n\n  // Get payment status color\n  getPaymentStatusColor(status: string): 'success' | 'warning' | 'error' | 'default' {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'pending':\n        return 'warning';\n      case 'failed':\n        return 'error';\n      default:\n        return 'default';\n    }\n  }\n}\n\nexport default new CreditService();\n"], "mappings": "AAAA,OAAOA,GAAG,MAAqB,OAAO;AAqEtC,MAAMC,aAAa,CAAC;EAClB;EACA,MAAMC,UAAUA,CAAA,EAA2B;IACzC,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC;IACjD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMC,WAAWA,CAAA,EAA6B;IAC5C,MAAMH,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,kBAAkB,CAAC;IAClD,OAAOD,QAAQ,CAACE,IAAI,CAACE,QAAQ;EAC/B;;EAEA;EACA,MAAMC,eAAeA,CAACC,IAAY,GAAG,CAAC,EAQnC;IACD,MAAMN,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,6BAA6BK,IAAI,EAAE,CAAC;IACnE,OAAON,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMK,aAAaA,CAAA,EAA8B;IAC/C,MAAMP,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,oBAAoB,CAAC;IACpD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMM,aAAaA,CAACC,SAAiB,EAAEC,WAAoB,EAA4B;IACrF,MAAMV,QAAQ,GAAG,MAAMH,GAAG,CAACc,IAAI,CAAC,iBAAiB,EAAE;MACjDC,UAAU,EAAEH,SAAS;MACrBI,YAAY,EAAEH;IAChB,CAAC,CAAC;IACF,OAAOV,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMY,kBAAkBA,CAACC,aAAqB,EAA0B;IACtE,MAAMf,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,EAAE;MAChDe,MAAM,EAAE;QAAEC,cAAc,EAAEF;MAAc;IAC1C,CAAC,CAAC;IACF,OAAOf,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACA,MAAMgB,gBAAgBA,CAAA,EAA2B;IAC/C,MAAMlB,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,iBAAiB,CAAC;IACjD,OAAOD,QAAQ,CAACE,IAAI;EACtB;;EAEA;EACAiB,aAAaA,CAACC,MAAiC,EAAU;IACvD,MAAMC,UAAU,GAAGD,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,CAAC;IAC9B,OAAO,GAAGC,UAAU,CAACC,cAAc,CAAC,CAAC,UAAU;EACjD;;EAEA;EACAC,cAAcA,CAACH,MAAiC,EAAU;IACxD,MAAMC,UAAU,GAAGD,MAAM,aAANA,MAAM,cAANA,MAAM,GAAI,CAAC;IAC9B,OAAO,MAAMC,UAAU,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;EACtC;;EAEA;EACAC,uBAAuBA,CAACC,IAAY,EAAwD;IAC1F,QAAQA,IAAI;MACV,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,OAAO;QACV,OAAO,MAAM;MACf;QACE,OAAO,SAAS;IACpB;EACF;;EAEA;EACAC,qBAAqBA,CAACC,MAAc,EAA+C;IACjF,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF;AACF;AAEA,eAAe,IAAI9B,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}