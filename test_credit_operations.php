<?php

require_once 'backend/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\CreditTransaction;

// Bootstrap Laravel
$app = new Application(realpath('backend'));
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);
$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== CREDIT SYSTEM TESTING ===\n\n";

try {
    // Test 1: Check if credit_balance column exists in users table
    echo "1. Testing database schema...\n";
    $columns = DB::select("DESCRIBE users");
    $hasCreditBalance = false;
    foreach ($columns as $column) {
        if ($column->Field === 'credit_balance') {
            $hasCreditBalance = true;
            echo "   ✅ credit_balance column exists (Type: {$column->Type}, Default: {$column->Default})\n";
            break;
        }
    }
    if (!$hasCreditBalance) {
        echo "   ❌ credit_balance column not found\n";
        exit(1);
    }

    // Test 2: Find or create a test user
    echo "\n2. Setting up test user...\n";
    $testUser = User::where('email', '<EMAIL>')->first();
    if (!$testUser) {
        $testUser = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'user',
            'credit_balance' => 0,
            'email_verified_at' => now(),
        ]);
        echo "   ✅ Created test user (ID: {$testUser->id})\n";
    } else {
        // Reset credit balance for testing
        $testUser->update(['credit_balance' => 0]);
        echo "   ✅ Using existing test user (ID: {$testUser->id})\n";
    }

    // Test 3: Test addCredits method
    echo "\n3. Testing addCredits method...\n";
    $initialBalance = $testUser->credit_balance;
    echo "   Initial balance: {$initialBalance}\n";
    
    $testUser->addCredits(100, 'Test credit addition');
    $testUser->refresh();
    
    echo "   Balance after adding 100 credits: {$testUser->credit_balance}\n";
    if ($testUser->credit_balance == $initialBalance + 100) {
        echo "   ✅ addCredits method works correctly\n";
    } else {
        echo "   ❌ addCredits method failed\n";
    }

    // Check if transaction was created
    $transaction = CreditTransaction::where('user_id', $testUser->id)
        ->where('type', 'credit')
        ->where('credit_amount', 100)
        ->latest()
        ->first();
    
    if ($transaction) {
        echo "   ✅ Credit transaction recorded (ID: {$transaction->id})\n";
    } else {
        echo "   ❌ Credit transaction not recorded\n";
    }

    // Test 4: Test hasCredits method
    echo "\n4. Testing hasCredits method...\n";
    $hasEnough = $testUser->hasCredits(50);
    $hasNotEnough = $testUser->hasCredits(200);
    
    if ($hasEnough && !$hasNotEnough) {
        echo "   ✅ hasCredits method works correctly\n";
        echo "   - Has 50 credits: " . ($hasEnough ? 'true' : 'false') . "\n";
        echo "   - Has 200 credits: " . ($hasNotEnough ? 'true' : 'false') . "\n";
    } else {
        echo "   ❌ hasCredits method failed\n";
    }

    // Test 5: Test deductCredits method (successful)
    echo "\n5. Testing deductCredits method (successful deduction)...\n";
    $balanceBeforeDeduction = $testUser->credit_balance;
    $deductionResult = $testUser->deductCredits(30, 'Test credit deduction');
    $testUser->refresh();
    
    if ($deductionResult && $testUser->credit_balance == $balanceBeforeDeduction - 30) {
        echo "   ✅ deductCredits method works correctly\n";
        echo "   Balance before: {$balanceBeforeDeduction}, After: {$testUser->credit_balance}\n";
    } else {
        echo "   ❌ deductCredits method failed\n";
    }

    // Check if deduction transaction was created
    $deductionTransaction = CreditTransaction::where('user_id', $testUser->id)
        ->where('type', 'usage')
        ->where('credit_amount', -30)
        ->latest()
        ->first();
    
    if ($deductionTransaction) {
        echo "   ✅ Deduction transaction recorded (ID: {$deductionTransaction->id})\n";
    } else {
        echo "   ❌ Deduction transaction not recorded\n";
    }

    // Test 6: Test deductCredits method (insufficient balance)
    echo "\n6. Testing deductCredits method (insufficient balance)...\n";
    $balanceBeforeFailedDeduction = $testUser->credit_balance;
    $failedDeductionResult = $testUser->deductCredits(1000, 'Test failed deduction');
    $testUser->refresh();
    
    if (!$failedDeductionResult && $testUser->credit_balance == $balanceBeforeFailedDeduction) {
        echo "   ✅ deductCredits correctly prevents overdraft\n";
        echo "   Balance unchanged: {$testUser->credit_balance}\n";
    } else {
        echo "   ❌ deductCredits failed to prevent overdraft\n";
    }

    // Test 7: Test credit transaction relationships
    echo "\n7. Testing credit transaction relationships...\n";
    $userTransactions = $testUser->creditTransactions()->count();
    echo "   Total transactions for user: {$userTransactions}\n";
    
    if ($userTransactions >= 2) {
        echo "   ✅ Credit transaction relationship works\n";
    } else {
        echo "   ❌ Credit transaction relationship failed\n";
    }

    // Test 8: Test transaction types and amounts
    echo "\n8. Testing transaction details...\n";
    $creditTransactions = $testUser->creditTransactions()
        ->where('type', 'credit')
        ->sum('credit_amount');
    $usageTransactions = abs($testUser->creditTransactions()
        ->where('type', 'usage')
        ->sum('credit_amount'));
    
    echo "   Total credits added: {$creditTransactions}\n";
    echo "   Total credits used: {$usageTransactions}\n";
    echo "   Current balance: {$testUser->credit_balance}\n";
    echo "   Expected balance: " . ($creditTransactions - $usageTransactions) . "\n";
    
    if ($testUser->credit_balance == ($creditTransactions - $usageTransactions)) {
        echo "   ✅ Transaction amounts match balance\n";
    } else {
        echo "   ❌ Transaction amounts don't match balance\n";
    }

    echo "\n=== CREDIT SYSTEM TEST COMPLETED ===\n";
    echo "All core credit operations are functioning correctly!\n";

} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
