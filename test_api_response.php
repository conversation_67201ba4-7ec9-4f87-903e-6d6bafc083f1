<?php

echo "=== TESTING API <NAME_EMAIL> ===\n\n";

// Change to backend directory
chdir('backend');

// Create script to test API response
$testScript = '
use App\Models\User;

echo "Testing API <NAME_EMAIL>...\n";

$admin = User::where("email", "<EMAIL>")->first();

if (!$admin) {
    echo "❌ User not found\n";
    exit;
}

echo "✅ Found user: {$admin->name}\n";
echo "Credit balance: {$admin->credit_balance}\n\n";

// Simulate the statistics calculation from CreditController
$totalPurchased = $admin->creditTransactions()
    ->where("type", "purchase")
    ->where("payment_status", "completed")
    ->sum("credit_amount") ?? 0;

$totalUsed = abs($admin->creditTransactions()
    ->where("type", "usage")
    ->sum("credit_amount") ?? 0);

$totalSpent = $admin->creditTransactions()
    ->where("type", "purchase")
    ->where("payment_status", "completed")
    ->sum("amount_paid") ?? 0;

echo "API Response Data:\n";
echo "current_balance: " . var_export($admin->credit_balance, true) . " (type: " . gettype($admin->credit_balance) . ")\n";
echo "total_purchased: " . var_export($totalPurchased, true) . " (type: " . gettype($totalPurchased) . ")\n";
echo "total_used: " . var_export($totalUsed, true) . " (type: " . gettype($totalUsed) . ")\n";
echo "total_spent: " . var_export($totalSpent, true) . " (type: " . gettype($totalSpent) . ")\n";

// Test JSON encoding
$response = [
    "current_balance" => $admin->credit_balance,
    "total_purchased" => $totalPurchased,
    "total_used" => $totalUsed,
    "total_spent" => $totalSpent,
];

echo "\nJSON Response:\n";
echo json_encode($response, JSON_PRETTY_PRINT) . "\n";
';

// Write test script
file_put_contents('temp_api_test.php', "<?php\n" . $testScript);

// Execute the test
echo "Running API response test...\n\n";
$output = shell_exec('php artisan tinker < temp_api_test.php 2>&1');

if ($output) {
    echo $output;
} else {
    echo "No output received\n";
}

// Clean up
if (file_exists('temp_api_test.php')) {
    unlink('temp_api_test.php');
}

echo "\n=== API RESPONSE TEST COMPLETE ===\n";
