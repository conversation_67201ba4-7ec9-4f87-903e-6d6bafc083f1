<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class AdminCreditTransactionApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $adminUser;
    protected User $regularUser;
    protected User $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->regularUser = User::factory()->create(['role' => 'user']);
        $this->testUser = User::factory()->create(['credit_balance' => 1000]);
    }

    /** @test */
    public function admin_can_create_purchase_transaction()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/credit-transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'amount_paid' => 50.00,
            'payment_method' => 'manual',
            'payment_status' => 'completed',
            'description' => 'Test purchase transaction',
        ]);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Transaction created successfully',
                ]);

        $this->assertDatabaseHas('credit_transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function admin_can_create_usage_transaction()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/credit-transactions/usage', [
            'user_id' => $this->testUser->id,
            'credit_amount' => 200,
            'description' => 'Test usage transaction',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('credit_transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'usage',
            'credit_amount' => -200, // Should be negative
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function admin_can_create_bonus_transaction()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/credit-transactions/bonus', [
            'user_id' => $this->testUser->id,
            'credit_amount' => 100,
            'description' => 'Test bonus transaction',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('credit_transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'bonus',
            'credit_amount' => 100,
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function admin_can_create_refund_transaction()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/credit-transactions/refund', [
            'user_id' => $this->testUser->id,
            'credit_amount' => 300,
            'description' => 'Test refund transaction',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('credit_transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'refund',
            'credit_amount' => 300,
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function admin_can_create_adjustment_transaction()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->postJson('/api/admin/credit-transactions/adjustment', [
            'user_id' => $this->testUser->id,
            'credit_amount' => -50,
            'description' => 'Test negative adjustment',
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('credit_transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'adjustment',
            'credit_amount' => -50,
            'payment_status' => 'completed',
        ]);
    }

    /** @test */
    public function regular_user_cannot_access_admin_endpoints()
    {
        Sanctum::actingAs($this->regularUser);

        $response = $this->postJson('/api/admin/credit-transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
        ]);

        $response->assertStatus(403)
                ->assertJson([
                    'error' => 'Unauthorized. Admin access required.',
                ]);
    }

    /** @test */
    public function unauthenticated_user_cannot_access_admin_endpoints()
    {
        $response = $this->postJson('/api/admin/credit-transactions', [
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
        ]);

        $response->assertStatus(401);
    }

    /** @test */
    public function admin_can_get_transaction_types()
    {
        Sanctum::actingAs($this->adminUser);

        $response = $this->getJson('/api/admin/credit-transactions/types');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'transaction_types' => [
                        'purchase' => [
                            'label',
                            'description',
                            'credit_amount_sign',
                            'requires_payment_amount',
                            'requires_payment_method',
                            'default_payment_status',
                            'allows_package_selection',
                        ],
                        'usage',
                        'refund',
                        'bonus',
                        'adjustment',
                    ],
                ]);
    }

    /** @test */
    public function admin_can_verify_user_balance()
    {
        Sanctum::actingAs($this->adminUser);

        // Create a transaction that should affect balance
        CreditTransaction::create([
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);

        // Set incorrect balance
        $this->testUser->update(['credit_balance' => 999]);

        $response = $this->postJson('/api/admin/credit-transactions/verify-balance', [
            'user_id' => $this->testUser->id,
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'balance_verification' => [
                        'user_id',
                        'current_balance',
                        'expected_balance',
                        'balance_correct',
                        'fixed',
                    ],
                ]);
    }

    /** @test */
    public function admin_can_get_balance_breakdown()
    {
        Sanctum::actingAs($this->adminUser);

        // Create various transactions
        CreditTransaction::create([
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->testUser->id,
            'type' => 'usage',
            'credit_amount' => -200,
            'payment_status' => 'completed',
        ]);

        $response = $this->getJson('/api/admin/credit-transactions/balance-breakdown?user_id=' . $this->testUser->id);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'user_id' => $this->testUser->id,
                ])
                ->assertJsonStructure([
                    'success',
                    'user_id',
                    'current_balance',
                    'breakdown' => [
                        'purchase' => [
                            'total_credits',
                            'transaction_count',
                        ],
                        'usage',
                        'refund',
                        'bonus',
                        'adjustment',
                    ],
                ]);
    }

    /** @test */
    public function admin_can_get_transaction_statistics()
    {
        Sanctum::actingAs($this->adminUser);

        // Create some test transactions
        CreditTransaction::create([
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 500,
            'payment_status' => 'completed',
        ]);

        CreditTransaction::create([
            'user_id' => $this->testUser->id,
            'type' => 'purchase',
            'credit_amount' => 300,
            'payment_status' => 'pending',
        ]);

        $response = $this->getJson('/api/admin/credit-transactions/statistics');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                ])
                ->assertJsonStructure([
                    'success',
                    'statistics' => [
                        'purchase' => [
                            'total_transactions',
                            'completed_transactions',
                            'pending_transactions',
                            'total_credits',
                            'completed_credits',
                            'total_amount_paid',
                        ],
                        'usage',
                        'refund',
                        'bonus',
                        'adjustment',
                    ],
                ]);
    }

    /** @test */
    public function transaction_creation_validates_insufficient_credits()
    {
        Sanctum::actingAs($this->adminUser);
        
        // Set low balance
        $this->testUser->update(['credit_balance' => 50]);

        $response = $this->postJson('/api/admin/credit-transactions/usage', [
            'user_id' => $this->testUser->id,
            'credit_amount' => 100, // More than available
        ]);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                ]);
    }
}
