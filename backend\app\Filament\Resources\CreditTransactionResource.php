<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CreditTransactionResource\Pages;
use App\Filament\Resources\CreditTransactionResource\RelationManagers;
use App\Models\CreditTransaction;
use App\Services\CreditTransactionService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CreditTransactionResource extends Resource
{
    protected static ?string $model = CreditTransaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Credit Transactions';
    protected static ?string $modelLabel = 'Credit Transaction';
    protected static ?string $pluralModelLabel = 'Credit Transactions';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Transaction Details')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->relationship('user', 'name')
                            ->required()
                            ->searchable()
                            ->columnSpanFull(),

                        Forms\Components\Select::make('type')
                            ->options([
                                'purchase' => 'Purchase',
                                'usage' => 'Usage',
                                'refund' => 'Refund',
                                'bonus' => 'Bonus',
                                'adjustment' => 'Adjustment',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                if ($state) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($state);

                                    // Set default payment status
                                    $set('payment_status', $config['default_payment_status']);

                                    // Clear fields that might not be applicable
                                    if (!$config['requires_payment_amount']) {
                                        $set('amount_paid', null);
                                    }

                                    if (!$config['requires_payment_method']) {
                                        $set('payment_method', $state === 'usage' || $state === 'bonus' ? 'system' : null);
                                    }

                                    if (!$config['allows_package_selection']) {
                                        $set('credit_package_id', null);
                                    }

                                    // Set processed_at for completed transactions
                                    if ($config['default_payment_status'] === 'completed') {
                                        $set('processed_at', now());
                                    }
                                }
                            })
                            ->helperText(function ($state) {
                                if ($state) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($state);
                                    return $config['description'];
                                }
                                return 'Select a transaction type to see specific requirements';
                            }),

                        Forms\Components\Select::make('credit_package_id')
                            ->relationship('creditPackage', 'name')
                            ->visible(fn (Forms\Get $get) => in_array($get('type'), ['purchase']))
                            ->helperText('Optional: Select if this transaction is related to a specific package'),

                        Forms\Components\TextInput::make('credit_amount')
                            ->required()
                            ->numeric()
                            ->suffix('credits')
                            ->helperText(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);

                                    switch ($config['credit_amount_sign']) {
                                        case 'positive':
                                            return 'Enter a positive number (credits will be added)';
                                        case 'negative':
                                            return 'Enter a positive number (will be converted to negative for usage)';
                                        case 'flexible':
                                            return 'Enter positive number to add credits, negative to deduct';
                                        default:
                                            return 'Enter the credit amount';
                                    }
                                }
                                return 'Enter the credit amount';
                            }),

                        Forms\Components\TextInput::make('amount_paid')
                            ->numeric()
                            ->prefix('RM')
                            ->step(0.01)
                            ->visible(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_amount'];
                                }
                                return true;
                            })
                            ->required(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_amount'];
                                }
                                return false;
                            }),

                        Forms\Components\Select::make('payment_method')
                            ->options([
                                'billplz' => 'Billplz',
                                'manual' => 'Manual',
                                'system' => 'System',
                            ])
                            ->visible(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_method'];
                                }
                                return true;
                            })
                            ->required(function (Forms\Get $get) {
                                $type = $get('type');
                                if ($type) {
                                    $service = new CreditTransactionService();
                                    $config = $service->getTransactionTypeConfig($type);
                                    return $config['requires_payment_method'];
                                }
                                return false;
                            }),

                        Forms\Components\TextInput::make('payment_reference')
                            ->maxLength(255)
                            ->helperText('Optional: Payment gateway reference or transaction ID'),

                        Forms\Components\Select::make('payment_status')
                            ->options([
                                'pending' => 'Pending',
                                'completed' => 'Completed',
                                'failed' => 'Failed',
                                'refunded' => 'Refunded',
                            ])
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set, $state) {
                                if ($state === 'completed') {
                                    $set('processed_at', now());
                                } else {
                                    $set('processed_at', null);
                                }
                            }),

                        Forms\Components\DateTimePicker::make('processed_at')
                            ->visible(fn (Forms\Get $get) => $get('payment_status') === 'completed'),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('Optional: Will be auto-generated if left empty'),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\KeyValue::make('metadata')
                            ->label('Metadata')
                            ->helperText('Additional payment gateway data in key-value format')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'purchase' => 'success',
                        'usage' => 'warning',
                        'refund' => 'danger',
                        'bonus' => 'info',
                        'adjustment' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('credit_amount')
                    ->suffix(' credits')
                    ->color(fn ($state) => $state > 0 ? 'success' : 'danger')
                    ->sortable(),

                Tables\Columns\TextColumn::make('amount_paid')
                    ->money('MYR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('payment_method')
                    ->badge(),

                Tables\Columns\TextColumn::make('payment_status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'completed' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        'refunded' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('processed_at')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('metadata')
                    ->label('Metadata')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            return json_encode($state, JSON_PRETTY_PRINT);
                        }
                        return 'No metadata';
                    })
                    ->limit(50)
                    ->tooltip(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            return json_encode($state, JSON_PRETTY_PRINT);
                        }
                        return null;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'purchase' => 'Purchase',
                        'usage' => 'Usage',
                        'refund' => 'Refund',
                        'bonus' => 'Bonus',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\SelectFilter::make('payment_status')
                    ->options([
                        'pending' => 'Pending',
                        'completed' => 'Completed',
                        'failed' => 'Failed',
                        'refunded' => 'Refunded',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditTransactions::route('/'),
            'create' => Pages\CreateCreditTransaction::route('/create'),
            'view' => Pages\ViewCreditTransaction::route('/{record}'),
            'edit' => Pages\EditCreditTransaction::route('/{record}/edit'),
        ];
    }
}
