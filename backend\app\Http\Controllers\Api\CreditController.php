<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CreditPackage;
use App\Models\CreditTransaction;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CreditController extends Controller
{
    /**
     * Get user's credit balance
     */
    public function balance(Request $request): JsonResponse
    {
        $user = $request->user();

        return response()->json([
            'credit_balance' => $user->credit_balance,
            'user_id' => $user->id,
        ]);
    }

    /**
     * Get available credit packages
     */
    public function packages(): JsonResponse
    {
        $packages = CreditPackage::active()
            ->ordered()
            ->get()
            ->map(function ($package) {
                return [
                    'id' => $package->id,
                    'name' => $package->name,
                    'description' => $package->description,
                    'price' => $package->price,
                    'formatted_price' => $package->formatted_price,
                    'credit_amount' => $package->credit_amount,
                    'price_per_credit' => $package->price_per_credit,
                    'features' => $package->features,
                ];
            });

        return response()->json([
            'packages' => $packages,
        ]);
    }

    /**
     * Get user's credit transaction history
     */
    public function transactions(Request $request): JsonResponse
    {
        $user = $request->user();

        $transactions = $user->creditTransactions()
            ->with('creditPackage')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $transactions->getCollection()->transform(function ($transaction) {
            return [
                'id' => $transaction->id,
                'type' => $transaction->type,
                'credit_amount' => $transaction->credit_amount,
                'amount_paid' => $transaction->amount_paid,
                'formatted_amount_paid' => $transaction->formatted_amount_paid,
                'payment_method' => $transaction->payment_method,
                'payment_status' => $transaction->payment_status,
                'description' => $transaction->description,
                'package_name' => $transaction->creditPackage?->name,
                'is_credit' => $transaction->is_credit,
                'is_debit' => $transaction->is_debit,
                'processed_at' => $transaction->processed_at?->format('Y-m-d H:i:s'),
                'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
            ];
        });

        return response()->json([
            'transactions' => $transactions,
        ]);
    }

    /**
     * Get credit statistics for user
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();

        $totalPurchased = $user->creditTransactions()
            ->where('type', 'purchase')
            ->where('payment_status', 'completed')
            ->sum('credit_amount') ?? 0;

        $totalUsed = abs($user->creditTransactions()
            ->where('type', 'usage')
            ->sum('credit_amount') ?? 0);

        $totalSpent = $user->creditTransactions()
            ->where('type', 'purchase')
            ->where('payment_status', 'completed')
            ->sum('amount_paid') ?? 0;

        $recentTransactions = $user->creditTransactions()
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'credit_amount' => $transaction->credit_amount,
                    'description' => $transaction->description,
                    'created_at' => $transaction->created_at->format('Y-m-d H:i:s'),
                ];
            });

        return response()->json([
            'current_balance' => $user->credit_balance,
            'total_purchased' => $totalPurchased,
            'total_used' => $totalUsed,
            'total_spent' => $totalSpent,
            'recent_transactions' => $recentTransactions,
        ]);
    }
}
