<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\CreateCreditTransactionRequest;
use App\Models\CreditTransaction;
use App\Models\User;
use App\Services\CreditTransactionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CreditTransactionController extends Controller
{
    protected CreditTransactionService $transactionService;

    public function __construct(CreditTransactionService $transactionService)
    {
        $this->transactionService = $transactionService;
        
        // Ensure only admin users can access these endpoints
        $this->middleware(function ($request, $next) {
            if (!$request->user() || $request->user()->role !== 'admin') {
                return response()->json(['error' => 'Unauthorized. Admin access required.'], 403);
            }
            return $next($request);
        });
    }

    /**
     * Create a new credit transaction
     */
    public function store(CreateCreditTransactionRequest $request): JsonResponse
    {
        try {
            $transaction = $this->transactionService->createTransaction($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Transaction created successfully',
                'transaction' => [
                    'id' => $transaction->id,
                    'type' => $transaction->type,
                    'user_id' => $transaction->user_id,
                    'credit_amount' => $transaction->credit_amount,
                    'amount_paid' => $transaction->amount_paid,
                    'payment_method' => $transaction->payment_method,
                    'payment_status' => $transaction->payment_status,
                    'description' => $transaction->description,
                    'processed_at' => $transaction->processed_at,
                    'created_at' => $transaction->created_at,
                ],
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create transaction',
                'error' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * Get transaction type configurations
     */
    public function getTransactionTypes(): JsonResponse
    {
        $configs = $this->transactionService->getAllTransactionTypeConfigs();

        return response()->json([
            'success' => true,
            'transaction_types' => $configs,
        ]);
    }

    /**
     * Verify and fix user credit balance
     */
    public function verifyBalance(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $result = $this->transactionService->verifyAndFixBalance($user);

        return response()->json([
            'success' => true,
            'balance_verification' => $result,
        ]);
    }

    /**
     * Get detailed balance breakdown for a user
     */
    public function getBalanceBreakdown(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);
        $breakdown = $this->transactionService->getBalanceBreakdown($user);

        return response()->json([
            'success' => true,
            'user_id' => $user->id,
            'current_balance' => $user->credit_balance,
            'breakdown' => $breakdown,
        ]);
    }

    /**
     * Bulk verify and fix credit balances
     */
    public function bulkVerifyBalances(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => 'nullable|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $userIds = $request->input('user_ids');
        
        // If no specific users provided, check all users with transactions
        if (empty($userIds)) {
            $userIds = User::whereHas('creditTransactions')->pluck('id')->toArray();
        }

        $results = [];
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $results[] = $this->transactionService->verifyAndFixBalance($user);
            }
        }

        $fixedCount = collect($results)->where('fixed', true)->count();
        $totalCount = count($results);

        return response()->json([
            'success' => true,
            'message' => "Verified {$totalCount} users, fixed {$fixedCount} balances",
            'results' => $results,
        ]);
    }

    /**
     * Get transaction statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = DB::table('credit_transactions')
            ->select('type', 'payment_status')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(credit_amount) as total_credits')
            ->selectRaw('SUM(amount_paid) as total_amount_paid')
            ->groupBy('type', 'payment_status')
            ->get()
            ->groupBy('type');

        $summary = [];
        foreach (CreditTransactionService::VALID_TYPES as $type) {
            $typeStats = $stats->get($type, collect());
            
            $summary[$type] = [
                'total_transactions' => $typeStats->sum('count'),
                'completed_transactions' => $typeStats->where('payment_status', 'completed')->sum('count'),
                'pending_transactions' => $typeStats->where('payment_status', 'pending')->sum('count'),
                'total_credits' => $typeStats->sum('total_credits'),
                'completed_credits' => $typeStats->where('payment_status', 'completed')->sum('total_credits'),
                'total_amount_paid' => $typeStats->sum('total_amount_paid'),
            ];
        }

        return response()->json([
            'success' => true,
            'statistics' => $summary,
        ]);
    }

    /**
     * Create a specific transaction type with simplified parameters
     */
    public function createPurchase(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'purchase');
    }

    public function createUsage(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'usage');
    }

    public function createRefund(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'refund');
    }

    public function createBonus(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'bonus');
    }

    public function createAdjustment(Request $request): JsonResponse
    {
        return $this->createSpecificTransaction($request, 'adjustment');
    }

    /**
     * Helper method to create specific transaction types
     */
    protected function createSpecificTransaction(Request $request, string $type): JsonResponse
    {
        $data = $request->all();
        $data['type'] = $type;

        // Create a new request instance with the type set
        $createRequest = new CreateCreditTransactionRequest();
        $createRequest->replace($data);
        $createRequest->setUserResolver($request->getUserResolver());

        return $this->store($createRequest);
    }
}
