<?php

namespace App\Http\Requests;

use App\Services\CreditTransactionService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateCreditTransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Only allow admin users to create transactions
        return $this->user() && $this->user()->role === 'admin';
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $transactionService = new CreditTransactionService();
        $type = $this->input('type');
        
        $rules = [
            'user_id' => 'required|exists:users,id',
            'type' => ['required', Rule::in(CreditTransactionService::VALID_TYPES)],
            'credit_amount' => 'required|numeric',
            'description' => 'nullable|string|max:1000',
            'payment_reference' => 'nullable|string|max:255',
            'metadata' => 'nullable|array',
        ];

        // Add type-specific validation rules
        if ($type) {
            $config = $transactionService->getTransactionTypeConfig($type);
            
            // Payment amount validation
            if ($config['requires_payment_amount']) {
                $rules['amount_paid'] = 'required|numeric|min:0';
            } else {
                $rules['amount_paid'] = 'nullable|numeric|min:0';
            }
            
            // Payment method validation
            if ($config['requires_payment_method']) {
                $rules['payment_method'] = 'required|in:billplz,manual,system';
            } else {
                $rules['payment_method'] = 'nullable|in:billplz,manual,system';
            }
            
            // Credit package validation
            if ($config['allows_package_selection']) {
                $rules['credit_package_id'] = 'nullable|exists:credit_packages,id';
            } else {
                $rules['credit_package_id'] = 'nullable';
            }
            
            // Payment status validation
            $rules['payment_status'] = 'required|in:pending,completed,failed,refunded';
            
            // Credit amount sign validation
            switch ($config['credit_amount_sign']) {
                case 'positive':
                    $rules['credit_amount'] .= '|min:1';
                    break;
                case 'negative':
                    $rules['credit_amount'] .= '|max:-1';
                    break;
                case 'flexible':
                    // No additional constraint for adjustments
                    break;
            }
        }

        return $rules;
    }

    /**
     * Get custom validation messages
     */
    public function messages(): array
    {
        return [
            'user_id.required' => 'Please select a user for this transaction.',
            'user_id.exists' => 'The selected user does not exist.',
            'type.required' => 'Please select a transaction type.',
            'type.in' => 'Invalid transaction type selected.',
            'credit_amount.required' => 'Credit amount is required.',
            'credit_amount.numeric' => 'Credit amount must be a number.',
            'credit_amount.min' => 'Credit amount must be positive for this transaction type.',
            'credit_amount.max' => 'Credit amount must be negative for usage transactions.',
            'amount_paid.required' => 'Payment amount is required for this transaction type.',
            'amount_paid.numeric' => 'Payment amount must be a number.',
            'amount_paid.min' => 'Payment amount cannot be negative.',
            'payment_method.required' => 'Payment method is required for this transaction type.',
            'payment_method.in' => 'Invalid payment method selected.',
            'payment_status.required' => 'Payment status is required.',
            'payment_status.in' => 'Invalid payment status selected.',
            'credit_package_id.exists' => 'The selected credit package does not exist.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'payment_reference.max' => 'Payment reference cannot exceed 255 characters.',
            'metadata.array' => 'Metadata must be a valid array.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $type = $this->input('type');
            $creditAmount = $this->input('credit_amount');
            $userId = $this->input('user_id');
            
            if ($type && $creditAmount && $userId) {
                $transactionService = new CreditTransactionService();
                
                try {
                    // Validate business rules using the service
                    $data = $this->validated();
                    $processedData = $transactionService->processTransactionData($data);
                    $transactionService->validateTransactionData($processedData);
                } catch (\Illuminate\Validation\ValidationException $e) {
                    foreach ($e->errors() as $field => $messages) {
                        foreach ($messages as $message) {
                            $validator->errors()->add($field, $message);
                        }
                    }
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Ensure processed_at is set for completed transactions
        if ($this->input('payment_status') === 'completed' && !$this->input('processed_at')) {
            $this->merge([
                'processed_at' => now(),
            ]);
        }
    }
}
