<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Console\Command;

class InvestigateCreditBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:investigate {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Investigate credit balance discrepancies for a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info("🔍 INVESTIGATING CREDIT BALANCE FOR: {$email}");
        $this->line("=" . str_repeat("=", 60));
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("❌ User not found: {$email}");
            return;
        }
        
        $this->info("✅ Found user: {$user->name} (ID: {$user->id})");
        $this->info("📊 Current credit_balance in database: {$user->credit_balance}");
        $this->line("");
        
        // Get ALL transactions for detailed analysis
        $transactions = $user->creditTransactions()->orderBy('created_at', 'desc')->get();
        $this->info("📝 Total transactions found: " . $transactions->count());
        $this->line("");
        
        if ($transactions->count() === 0) {
            $this->warn("No transactions found for this user");
            return;
        }
        
        // Display all transactions with details
        $this->info("📋 DETAILED TRANSACTION HISTORY:");
        $this->line(str_repeat("-", 80));
        
        $runningBalance = 0;
        $transactionsByType = [];
        
        foreach ($transactions->reverse() as $index => $transaction) {
            $this->line(sprintf(
                "%2d. ID: %3d | %12s | %6s credits | %10s | %s",
                $index + 1,
                $transaction->id,
                $transaction->type,
                $transaction->credit_amount,
                $transaction->payment_status,
                $transaction->created_at->format('Y-m-d H:i:s')
            ));
            
            $this->line("    Description: {$transaction->description}");
            
            // Track by type for summary
            if (!isset($transactionsByType[$transaction->type])) {
                $transactionsByType[$transaction->type] = [
                    'count' => 0,
                    'total_amount' => 0,
                    'completed_amount' => 0
                ];
            }
            
            $transactionsByType[$transaction->type]['count']++;
            $transactionsByType[$transaction->type]['total_amount'] += $transaction->credit_amount;
            
            if ($transaction->payment_status === 'completed') {
                $transactionsByType[$transaction->type]['completed_amount'] += $transaction->credit_amount;
                $runningBalance += $transaction->credit_amount;
            }
            
            $this->line("    Running balance (if completed): {$runningBalance}");
            $this->line("");
        }
        
        // Summary by transaction type
        $this->info("📊 TRANSACTION SUMMARY BY TYPE:");
        $this->line(str_repeat("-", 60));
        
        $totalExpectedBalance = 0;
        
        foreach ($transactionsByType as $type => $data) {
            $this->line(sprintf(
                "%12s: %2d transactions | Total: %6s | Completed: %6s",
                ucfirst($type),
                $data['count'],
                $data['total_amount'],
                $data['completed_amount']
            ));
            
            // Add to expected balance (usage transactions are already negative)
            $totalExpectedBalance += $data['completed_amount'];
        }
        
        $this->line("");
        $this->info("🧮 BALANCE CALCULATION:");
        $this->line(str_repeat("-", 40));
        
        // Detailed calculation
        $purchaseCredits = $user->creditTransactions()
            ->where('type', 'purchase')
            ->where('payment_status', 'completed')
            ->sum('credit_amount');
            
        $bonusCredits = $user->creditTransactions()
            ->where('type', 'bonus')
            ->where('payment_status', 'completed')
            ->sum('credit_amount');
            
        $adjustmentCredits = $user->creditTransactions()
            ->where('type', 'adjustment')
            ->where('payment_status', 'completed')
            ->sum('credit_amount');
            
        $creditCredits = $user->creditTransactions()
            ->where('type', 'credit')
            ->where('payment_status', 'completed')
            ->sum('credit_amount');
            
        $usageCredits = $user->creditTransactions()
            ->where('type', 'usage')
            ->where('payment_status', 'completed')
            ->sum('credit_amount'); // This should be negative
            
        $refundCredits = $user->creditTransactions()
            ->where('type', 'refund')
            ->where('payment_status', 'completed')
            ->sum('credit_amount');
        
        $this->line("Purchase credits (completed): {$purchaseCredits}");
        $this->line("Bonus credits (completed):    {$bonusCredits}");
        $this->line("Adjustment credits:           {$adjustmentCredits}");
        $this->line("Credit credits:               {$creditCredits}");
        $this->line("Usage credits:                {$usageCredits}");
        $this->line("Refund credits:               {$refundCredits}");
        
        $calculatedBalance = $purchaseCredits + $bonusCredits + $adjustmentCredits + $creditCredits + $usageCredits + $refundCredits;
        
        $this->line("");
        $this->info("Expected balance: {$calculatedBalance}");
        $this->info("Actual balance:   {$user->credit_balance}");
        $this->info("Difference:       " . ($calculatedBalance - $user->credit_balance));
        
        if ($calculatedBalance != $user->credit_balance) {
            $this->error("❌ BALANCE MISMATCH DETECTED!");
            
            if ($this->confirm('Do you want to fix the balance?')) {
                $this->info("🔧 Fixing credit balance...");
                
                $user->update(['credit_balance' => $calculatedBalance]);
                $user->refresh();
                
                $this->info("✅ Balance updated from {$user->getOriginal('credit_balance')} to {$user->credit_balance}");
                
                // Verify the fix
                $this->info("🔍 Verification:");
                $this->info("New balance in database: {$user->credit_balance}");
                $this->info("Expected balance:        {$calculatedBalance}");
                
                if ($user->credit_balance == $calculatedBalance) {
                    $this->info("✅ Balance is now correct!");
                } else {
                    $this->error("❌ Balance is still incorrect after update");
                }
            }
        } else {
            $this->info("✅ Balance is correct!");
        }
        
        $this->line("");
        $this->info("🎯 INVESTIGATION COMPLETE");
    }
}
