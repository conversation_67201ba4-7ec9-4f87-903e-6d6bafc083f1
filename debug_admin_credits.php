<?php

echo "=== DEBUGGING <EMAIL> CREDIT ISSUE ===\n\n";

// Change to backend directory
chdir('backend');

// Create debug script for Laravel Tinker
$debugScript = '
use App\Models\User;
use App\Models\CreditTransaction;

echo "1. Finding <EMAIL> user...\n";
$admin = User::where("email", "<EMAIL>")->first();

if (!$admin) {
    echo "❌ User <EMAIL> not found!\n";
    echo "Available users:\n";
    $users = User::select("id", "email", "name", "credit_balance")->get();
    foreach ($users as $user) {
        echo "- {$user->email} (ID: {$user->id}, Balance: {$user->credit_balance})\n";
    }
    exit;
}

echo "✅ Found user: {$admin->name} (ID: {$admin->id})\n";
echo "📊 Current credit_balance field: {$admin->credit_balance}\n\n";

echo "2. Checking credit transactions...\n";
$transactions = $admin->creditTransactions()->orderBy("created_at", "desc")->get();
echo "📝 Total transactions: " . $transactions->count() . "\n\n";

if ($transactions->count() > 0) {
    echo "Transaction details:\n";
    foreach ($transactions as $transaction) {
        echo "- ID: {$transaction->id}\n";
        echo "  Type: {$transaction->type}\n";
        echo "  Credit Amount: {$transaction->credit_amount}\n";
        echo "  Description: {$transaction->description}\n";
        echo "  Payment Status: {$transaction->payment_status}\n";
        echo "  Created: {$transaction->created_at}\n";
        echo "  ---\n";
    }
    
    echo "\n3. Calculating expected balance...\n";
    $totalCredits = $transactions->where("type", "purchase")->sum("credit_amount");
    $totalUsage = abs($transactions->where("type", "usage")->sum("credit_amount"));
    $totalAdjustments = $transactions->where("type", "credit")->sum("credit_amount");
    $totalRefunds = $transactions->where("type", "refund")->sum("credit_amount");
    
    echo "💰 Total purchased: {$totalCredits}\n";
    echo "📉 Total used: {$totalUsage}\n";
    echo "🔧 Total adjustments: {$totalAdjustments}\n";
    echo "💸 Total refunds: {$totalRefunds}\n";
    
    $expectedBalance = $totalCredits + $totalAdjustments + $totalRefunds - $totalUsage;
    echo "🧮 Expected balance: {$expectedBalance}\n";
    echo "🏦 Actual balance: {$admin->credit_balance}\n";
    
    if ($expectedBalance != $admin->credit_balance) {
        echo "❌ BALANCE MISMATCH DETECTED!\n";
        echo "Difference: " . ($expectedBalance - $admin->credit_balance) . "\n";
        
        echo "\n4. Attempting to fix balance...\n";
        $admin->update(["credit_balance" => $expectedBalance]);
        $admin->refresh();
        echo "✅ Updated balance to: {$admin->credit_balance}\n";
    } else {
        echo "✅ Balance is correct\n";
    }
} else {
    echo "❌ No transactions found for this user\n";
}

echo "\n5. Checking if user exists in different email format...\n";
$possibleEmails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];
foreach ($possibleEmails as $email) {
    $user = User::where("email", $email)->first();
    if ($user) {
        echo "✅ Found user with email: {$email}\n";
        echo "   Name: {$user->name}\n";
        echo "   Balance: {$user->credit_balance}\n";
        echo "   Transactions: " . $user->creditTransactions()->count() . "\n";
    }
}
';

// Write debug script to temporary file
file_put_contents('temp_debug_admin.php', "<?php\n" . $debugScript);

// Execute the debug script
echo "Running debug analysis...\n\n";
$output = shell_exec('php artisan tinker < temp_debug_admin.php 2>&1');
echo $output;

// Clean up
unlink('temp_debug_admin.php');

echo "\n=== DEBUG ANALYSIS COMPLETE ===\n";
