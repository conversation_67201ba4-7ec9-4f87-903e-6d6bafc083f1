<?php

namespace App\Services;

use App\Models\PaymentSetting;
use App\Models\WalletTransaction;
use App\Models\User;
use App\Models\CreditPackage;
use Billplz\API;
use Billplz\Connect;
use Exception;
use Illuminate\Support\Facades\Log;

class BillplzService
{
    protected $api;
    protected $connect;
    protected $collectionId;
    protected $xSignatureKey;
    protected $isSandbox;
    protected $initialized = false;

    public function __construct()
    {
        // Don't initialize immediately to avoid errors during service container resolution
    }

    /**
     * Initialize Billplz client with settings
     */
    protected function initializeClient()
    {
        if ($this->initialized) {
            return;
        }
        $this->isSandbox = PaymentSetting::get('billplz_sandbox_mode', true);
        $apiKey = PaymentSetting::get('billplz_api_key');
        $this->collectionId = PaymentSetting::get('billplz_collection_id');
        $this->xSignatureKey = PaymentSetting::get('billplz_x_signature_key');

        if (!$apiKey) {
            throw new Exception('Billplz API key not configured');
        }

        if (!$this->collectionId) {
            throw new Exception('Billplz collection ID not configured');
        }

        // Log configuration for debugging
        Log::info('Billplz Service Configuration', [
            'sandbox_mode' => $this->isSandbox,
            'api_key_length' => strlen($apiKey),
            'collection_id' => $this->collectionId,
            'has_signature_key' => !empty($this->xSignatureKey),
        ]);

        $this->connect = new Connect($apiKey);
        $this->connect->setMode(!$this->isSandbox); // true for production, false for staging
        $this->api = new API($this->connect);

        // Test the connection by trying to get collection info
        try {
            $response = $this->api->getCollection($this->collectionId);
            if ($response[0] !== 200) {
                Log::error('Billplz Collection Access Failed', [
                    'status_code' => $response[0],
                    'response_body' => $response[1],
                    'collection_id' => $this->collectionId,
                    'sandbox_mode' => $this->isSandbox,
                ]);

                // Provide more specific error messages
                if ($response[0] === 403) {
                    throw new Exception('Billplz API access denied. Please check if your API key has access to collection ID: ' . $this->collectionId . ' in ' . ($this->isSandbox ? 'sandbox' : 'production') . ' mode.');
                } elseif ($response[0] === 401) {
                    throw new Exception('Billplz API authentication failed. Please check your API key.');
                } else {
                    throw new Exception('Billplz API error: HTTP ' . $response[0] . ' - ' . $response[1]);
                }
            }

            Log::info('Billplz Connection Test Successful', [
                'collection_id' => $this->collectionId,
            ]);

        } catch (Exception $e) {
            Log::error('Billplz Connection Test Failed', [
                'error' => $e->getMessage(),
                'collection_id' => $this->collectionId,
                'sandbox_mode' => $this->isSandbox,
            ]);
            throw $e;
        }

        $this->initialized = true;
    }

    /**
     * Create a bill for wallet top-up (credit package purchase)
     */
    public function createBill(User $user, CreditPackage $package, array $options = [])
    {
        $this->initializeClient();

        try {
            // Create pending wallet transaction record
            $transaction = WalletTransaction::create([
                'user_id' => $user->id,
                'package_id' => $package->id,
                'type' => 'top_up',
                'amount' => $package->price, // Amount to add to wallet (same as price paid)
                'amount_paid' => $package->price,
                'payment_method' => 'billplz',
                'payment_status' => 'pending',
                'description' => "Wallet top-up: {$package->name} package (RM " . number_format($package->price, 2) . ")",
            ]);

            $billData = [
                'collection_id' => $this->collectionId,
                'email' => $user->email,
                'name' => $user->name,
                'amount' => $package->price * 100, // Convert to cents
                'callback_url' => route('api.billplz.callback'),
                'description' => "Credit Package: {$package->name}",
                'reference_1_label' => 'Transaction ID',
                'reference_1' => $transaction->id,
                'reference_2_label' => 'User ID',
                'reference_2' => $user->id,
            ];

            // Add optional parameters
            if (isset($options['redirect_url'])) {
                $billData['redirect_url'] = $options['redirect_url'];
            }

            $response = $this->api->createBill($billData);

            if ($response[0] === 200) {
                $billData = json_decode($response[1], true);

                if (isset($billData['id'])) {
                    // Update transaction with bill ID
                    $transaction->update([
                        'payment_reference' => $billData['id'],
                        'metadata' => $billData,
                    ]);

                    return [
                        'success' => true,
                        'bill_id' => $billData['id'],
                        'url' => $billData['url'],
                        'transaction_id' => $transaction->id,
                        'data' => $billData,
                    ];
                }
            }

            throw new Exception('Failed to create bill: HTTP ' . $response[0] . ' - ' . $response[1]);

        } catch (Exception $e) {
            Log::error('Billplz bill creation failed', [
                'user_id' => $user->id,
                'package_id' => $package->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Handle Billplz callback
     */
    public function handleCallback(array $data)
    {
        $this->initializeClient();

        try {
            // Verify signature if X-Signature is provided
            if (isset($data['x_signature']) && $this->xSignatureKey) {
                if (!$this->verifySignature($data)) {
                    throw new Exception('Invalid signature');
                }
            }

            $billId = $data['id'] ?? null;
            $state = $data['state'] ?? null;

            if (!$billId) {
                throw new Exception('Bill ID not provided');
            }

            // Find transaction by bill ID
            $transaction = CreditTransaction::where('payment_reference', $billId)->first();

            if (!$transaction) {
                throw new Exception("Transaction not found for bill ID: {$billId}");
            }

            // Update transaction status based on bill state
            if ($state === 'paid') {
                $this->processSuccessfulPayment($transaction, $data);
            } else {
                $this->processFailedPayment($transaction, $data);
            }

            return [
                'success' => true,
                'transaction_id' => $transaction->id,
                'status' => $transaction->payment_status,
            ];

        } catch (Exception $e) {
            Log::error('Billplz callback processing failed', [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Process successful payment
     */
    protected function processSuccessfulPayment(CreditTransaction $transaction, array $data)
    {
        if ($transaction->payment_status === 'completed') {
            return; // Already processed
        }

        $transaction->update([
            'payment_status' => 'completed',
            'processed_at' => now(),
            'metadata' => array_merge($transaction->metadata ?? [], $data),
        ]);

        // Add credits to user account
        $user = $transaction->user;
        $user->increment('credit_balance', $transaction->credit_amount);

        Log::info('Payment processed successfully', [
            'transaction_id' => $transaction->id,
            'user_id' => $user->id,
            'credits_added' => $transaction->credit_amount,
        ]);
    }

    /**
     * Process failed payment
     */
    protected function processFailedPayment(CreditTransaction $transaction, array $data)
    {
        $transaction->update([
            'payment_status' => 'failed',
            'metadata' => array_merge($transaction->metadata ?? [], $data),
        ]);

        Log::info('Payment failed', [
            'transaction_id' => $transaction->id,
            'user_id' => $transaction->user_id,
            'bill_id' => $transaction->payment_reference,
        ]);
    }

    /**
     * Verify Billplz signature
     */
    protected function verifySignature(array $data): bool
    {
        $signature = $data['x_signature'] ?? '';
        unset($data['x_signature']);

        // Sort data by key
        ksort($data);

        // Create signature string
        $signatureString = '';
        foreach ($data as $key => $value) {
            $signatureString .= $key . $value;
        }

        $signatureString .= $this->xSignatureKey;

        $computedSignature = hash('sha256', $signatureString);

        return hash_equals($signature, $computedSignature);
    }

    /**
     * Get bill status
     */
    public function getBillStatus(string $billId)
    {
        $this->initializeClient();

        try {
            $response = $this->api->getBill($billId);

            if ($response[0] === 200) {
                $billData = json_decode($response[1], true);
                return [
                    'success' => true,
                    'data' => $billData,
                ];
            }

            return [
                'success' => false,
                'error' => 'HTTP ' . $response[0] . ' - ' . $response[1],
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check if Billplz is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty(PaymentSetting::get('billplz_api_key')) &&
               !empty(PaymentSetting::get('billplz_collection_id'));
    }

    /**
     * Check if Billplz is enabled
     */
    public function isEnabled(): bool
    {
        return PaymentSetting::get('billplz_enabled', false) && $this->isConfigured();
    }
}
