{"version": 1, "defects": {"Tests\\Feature\\CreditTransactionServiceTest::it_creates_purchase_transaction_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_creates_usage_transaction_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_creates_refund_transaction_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_creates_bonus_transaction_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_creates_adjustment_transaction_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_validates_insufficient_credits_for_usage": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_validates_insufficient_credits_for_negative_adjustment": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_calculates_expected_balance_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_verifies_and_fixes_balance": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_gets_balance_breakdown_correctly": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_validates_invalid_transaction_type": 8, "Tests\\Feature\\CreditTransactionServiceTest::it_validates_missing_user_id": 8}, "times": []}