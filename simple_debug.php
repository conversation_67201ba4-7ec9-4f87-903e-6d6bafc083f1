<?php

require_once 'backend/bootstrap/app.php';

$app = \Illuminate\Foundation\Application::configure(basePath: 'backend')
    ->withRouting(
        web: __DIR__.'/backend/routes/web.php',
        api: __DIR__.'/backend/routes/api.php',
        commands: __DIR__.'/backend/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (\Illuminate\Foundation\Configuration\Middleware $middleware) {
        //
    })
    ->withExceptions(function (\Illuminate\Foundation\Configuration\Exceptions $exceptions) {
        //
    })
    ->create();

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== SIMPLE <NAME_EMAIL> ===\n\n";

try {
    // Check all users
    echo "1. All users in database:\n";
    $users = \App\Models\User::select('id', 'email', 'name', 'credit_balance')->get();
    foreach ($users as $user) {
        echo "   - {$user->email} (Balance: {$user->credit_balance})\n";
    }
    
    // <NAME_EMAIL> specifically
    echo "\n2. <NAME_EMAIL>:\n";
    $admin = \App\Models\User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        echo "   ✅ Found <EMAIL>\n";
        echo "   ID: {$admin->id}\n";
        echo "   Name: {$admin->name}\n";
        echo "   Credit Balance: {$admin->credit_balance}\n";
        
        // Check transactions
        echo "\n3. Credit <NAME_EMAIL>:\n";
        $transactions = $admin->creditTransactions()->orderBy('created_at', 'desc')->get();
        echo "   Total transactions: " . $transactions->count() . "\n";
        
        foreach ($transactions as $transaction) {
            echo "   - {$transaction->type}: {$transaction->credit_amount} credits ({$transaction->created_at})\n";
        }
        
        // Calculate expected balance
        echo "\n4. Balance calculation:\n";
        $totalPurchased = $transactions->where('type', 'purchase')->sum('credit_amount');
        $totalUsed = abs($transactions->where('type', 'usage')->sum('credit_amount'));
        $totalCredits = $transactions->where('type', 'credit')->sum('credit_amount');
        
        echo "   Purchased: {$totalPurchased}\n";
        echo "   Used: {$totalUsed}\n";
        echo "   Credits: {$totalCredits}\n";
        echo "   Expected: " . ($totalPurchased + $totalCredits - $totalUsed) . "\n";
        echo "   Actual: {$admin->credit_balance}\n";
        
    } else {
        echo "   ❌ <EMAIL> not found\n";
        
        // Check for similar emails
        echo "\n   Looking for similar emails:\n";
        $similarUsers = \App\Models\User::where('email', 'like', '%admin%')->orWhere('email', 'like', '%cms%')->get();
        foreach ($similarUsers as $user) {
            echo "   - {$user->email}\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
