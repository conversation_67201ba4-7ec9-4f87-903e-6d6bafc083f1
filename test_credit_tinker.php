<?php

// Test script for Laravel Tinker
echo "=== CREDIT SYSTEM TESTING VIA TINKER ===\n";

// Test 1: Check User model and credit_balance field
echo "1. Testing User model...\n";
$user = App\Models\User::first();
if ($user) {
    echo "   ✅ User model accessible\n";
    echo "   Sample user: {$user->name} (ID: {$user->id})\n";
    echo "   Credit balance: {$user->credit_balance}\n";
} else {
    echo "   ❌ No users found in database\n";
}

// Test 2: Test addCredits method
echo "\n2. Testing addCredits method...\n";
if ($user) {
    $initialBalance = $user->credit_balance;
    echo "   Initial balance: {$initialBalance}\n";
    
    $user->addCredits(50, 'Test credit addition via tinker');
    $user->refresh();
    
    echo "   Balance after adding 50 credits: {$user->credit_balance}\n";
    if ($user->credit_balance == $initialBalance + 50) {
        echo "   ✅ addCredits method works correctly\n";
    } else {
        echo "   ❌ addCredits method failed\n";
    }
}

// Test 3: Test hasCredits method
echo "\n3. Testing hasCredits method...\n";
if ($user) {
    $hasEnough = $user->hasCredits(25);
    $hasNotEnough = $user->hasCredits(1000);
    
    echo "   Has 25 credits: " . ($hasEnough ? 'true' : 'false') . "\n";
    echo "   Has 1000 credits: " . ($hasNotEnough ? 'true' : 'false') . "\n";
    
    if ($hasEnough && !$hasNotEnough) {
        echo "   ✅ hasCredits method works correctly\n";
    } else {
        echo "   ❌ hasCredits method failed\n";
    }
}

// Test 4: Test deductCredits method
echo "\n4. Testing deductCredits method...\n";
if ($user) {
    $balanceBeforeDeduction = $user->credit_balance;
    $deductionResult = $user->deductCredits(20, 'Test credit deduction via tinker');
    $user->refresh();
    
    echo "   Balance before: {$balanceBeforeDeduction}, After: {$user->credit_balance}\n";
    echo "   Deduction result: " . ($deductionResult ? 'success' : 'failed') . "\n";
    
    if ($deductionResult && $user->credit_balance == $balanceBeforeDeduction - 20) {
        echo "   ✅ deductCredits method works correctly\n";
    } else {
        echo "   ❌ deductCredits method failed\n";
    }
}

// Test 5: Test credit transactions
echo "\n5. Testing credit transactions...\n";
if ($user) {
    $transactions = $user->creditTransactions()->latest()->take(3)->get();
    echo "   Recent transactions count: " . $transactions->count() . "\n";
    
    foreach ($transactions as $transaction) {
        echo "   - {$transaction->type}: {$transaction->credit_amount} credits ({$transaction->description})\n";
    }
    
    if ($transactions->count() > 0) {
        echo "   ✅ Credit transactions are being recorded\n";
    } else {
        echo "   ❌ No credit transactions found\n";
    }
}

echo "\n=== CREDIT SYSTEM TEST COMPLETED ===\n";
