<?php

namespace Database\Factories;

use App\Models\WalletTransaction;
use App\Models\User;
use App\Models\CreditPackage;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletTransaction>
 */
class WalletTransactionFactory extends Factory
{
    protected $model = WalletTransaction::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['top_up', 'payment', 'withdrawal', 'refund', 'bonus', 'adjustment'];
        $type = $this->faker->randomElement($types);
        
        // Determine amount based on type
        $amount = match ($type) {
            'top_up', 'refund', 'bonus' => $this->faker->randomFloat(2, 10, 500),
            'payment', 'withdrawal' => -$this->faker->randomFloat(2, 5, 200),
            'adjustment' => $this->faker->randomElement([
                $this->faker->randomFloat(2, 1, 100),
                -$this->faker->randomFloat(2, 1, 50)
            ]),
        };

        $paymentMethods = ['billplz', 'manual', 'system', 'bank_transfer'];
        $paymentStatuses = ['pending', 'completed', 'failed', 'refunded'];
        $withdrawalMethods = ['bank_transfer', 'paypal', 'manual'];

        return [
            'user_id' => User::factory(),
            'package_id' => $type === 'top_up' ? CreditPackage::factory() : null,
            'type' => $type,
            'amount' => $amount,
            'amount_paid' => $type === 'top_up' ? abs($amount) : null,
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'payment_reference' => $this->faker->optional()->regexify('[A-Z0-9]{10}'),
            'payment_status' => $this->faker->randomElement($paymentStatuses),
            'description' => $this->generateDescription($type, $amount),
            'processed_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 month', 'now'),
            'withdrawal_method' => $type === 'withdrawal' ? $this->faker->randomElement($withdrawalMethods) : null,
            'withdrawal_reference' => $type === 'withdrawal' ? $this->faker->optional()->regexify('[A-Z0-9]{12}') : null,
            'withdrawal_processed_at' => $type === 'withdrawal' ? $this->faker->optional(0.6)->dateTimeBetween('-1 month', 'now') : null,
            'metadata' => $this->faker->optional(0.3)->randomElements([
                'gateway_id' => $this->faker->uuid,
                'order_id' => $this->faker->numberBetween(1, 1000),
                'reference' => $this->faker->regexify('[A-Z0-9]{8}'),
            ], $this->faker->numberBetween(1, 3), false),
        ];
    }

    /**
     * Generate appropriate description based on transaction type
     */
    protected function generateDescription(string $type, float $amount): string
    {
        $formattedAmount = 'RM ' . number_format(abs($amount), 2);
        
        return match ($type) {
            'top_up' => "Wallet top-up: {$formattedAmount}",
            'payment' => "Payment deduction: {$formattedAmount}",
            'withdrawal' => "Wallet withdrawal: {$formattedAmount}",
            'refund' => "Refund credit: {$formattedAmount}",
            'bonus' => "Bonus credit: {$formattedAmount}",
            'adjustment' => $amount > 0 
                ? "Positive adjustment: {$formattedAmount}" 
                : "Negative adjustment: {$formattedAmount}",
        };
    }

    /**
     * Create a top-up transaction
     */
    public function topUp(float $amount = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'top_up',
            'amount' => $amount ?? $this->faker->randomFloat(2, 10, 500),
            'amount_paid' => $amount ?? $this->faker->randomFloat(2, 10, 500),
            'payment_method' => 'billplz',
            'package_id' => CreditPackage::factory(),
        ]);
    }

    /**
     * Create a payment transaction
     */
    public function payment(float $amount = null): static
    {
        $paymentAmount = $amount ? -abs($amount) : -$this->faker->randomFloat(2, 5, 200);
        
        return $this->state(fn (array $attributes) => [
            'type' => 'payment',
            'amount' => $paymentAmount,
            'amount_paid' => null,
            'payment_method' => 'system',
            'package_id' => null,
        ]);
    }

    /**
     * Create a withdrawal transaction
     */
    public function withdrawal(float $amount = null): static
    {
        $withdrawalAmount = $amount ? -abs($amount) : -$this->faker->randomFloat(2, 10, 300);
        
        return $this->state(fn (array $attributes) => [
            'type' => 'withdrawal',
            'amount' => $withdrawalAmount,
            'amount_paid' => null,
            'payment_method' => 'manual',
            'withdrawal_method' => $this->faker->randomElement(['bank_transfer', 'paypal', 'manual']),
            'withdrawal_reference' => $this->faker->regexify('[A-Z0-9]{12}'),
            'package_id' => null,
        ]);
    }

    /**
     * Create a refund transaction
     */
    public function refund(float $amount = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'refund',
            'amount' => $amount ?? $this->faker->randomFloat(2, 5, 100),
            'amount_paid' => null,
            'payment_method' => 'system',
            'package_id' => null,
        ]);
    }

    /**
     * Create a bonus transaction
     */
    public function bonus(float $amount = null): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'bonus',
            'amount' => $amount ?? $this->faker->randomFloat(2, 5, 50),
            'amount_paid' => null,
            'payment_method' => 'system',
            'package_id' => null,
        ]);
    }

    /**
     * Create an adjustment transaction
     */
    public function adjustment(float $amount = null): static
    {
        $adjustmentAmount = $amount ?? $this->faker->randomElement([
            $this->faker->randomFloat(2, 1, 100),
            -$this->faker->randomFloat(2, 1, 50)
        ]);
        
        return $this->state(fn (array $attributes) => [
            'type' => 'adjustment',
            'amount' => $adjustmentAmount,
            'amount_paid' => null,
            'payment_method' => 'manual',
            'package_id' => null,
        ]);
    }

    /**
     * Create a completed transaction
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'completed',
            'processed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Create a pending transaction
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'pending',
            'processed_at' => null,
        ]);
    }

    /**
     * Create a failed transaction
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'failed',
            'processed_at' => null,
        ]);
    }

    /**
     * Add metadata to the transaction
     */
    public function withMetadata(array $metadata): static
    {
        return $this->state(fn (array $attributes) => [
            'metadata' => $metadata,
        ]);
    }

    /**
     * Create transaction for specific user
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Create transaction with specific package
     */
    public function withPackage(CreditPackage $package): static
    {
        return $this->state(fn (array $attributes) => [
            'package_id' => $package->id,
        ]);
    }
}
