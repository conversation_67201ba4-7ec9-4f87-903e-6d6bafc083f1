<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\CreditTransaction;
use Illuminate\Console\Command;

class FixCreditBalances extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'credit:fix-balances {email?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix credit balances by recalculating from transactions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        if ($email) {
            $this->fixUserBalance($email);
        } else {
            $this->fixAllBalances();
        }
    }
    
    private function fixUserBalance($email)
    {
        $this->info("Fixing credit balance for: {$email}");
        
        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User not found: {$email}");
            $this->info("Available users:");
            $users = User::select('email', 'credit_balance')->get();
            foreach ($users as $u) {
                $this->line("  - {$u->email} (Balance: {$u->credit_balance})");
            }
            return;
        }
        
        $this->info("Found user: {$user->name} (ID: {$user->id})");
        $this->info("Current balance: {$user->credit_balance}");
        
        // Get all transactions
        $transactions = $user->creditTransactions()->orderBy('created_at', 'desc')->get();
        $this->info("Total transactions: " . $transactions->count());
        
        if ($transactions->count() > 0) {
            $this->info("\nTransaction Analysis:");
            
            $pendingCount = 0;
            $completedCount = 0;
            
            foreach ($transactions as $transaction) {
                $status = $transaction->payment_status ?: 'unknown';
                $this->line("- ID: {$transaction->id} | Type: {$transaction->type} | Amount: {$transaction->credit_amount} | Status: {$status}");
                
                if ($status === 'pending') {
                    $pendingCount++;
                } elseif ($status === 'completed') {
                    $completedCount++;
                }
            }
            
            $this->info("\nSummary:");
            $this->info("Pending: {$pendingCount}");
            $this->info("Completed: {$completedCount}");
            
            // Fix pending transactions
            if ($pendingCount > 0) {
                $this->info("\nFixing pending transactions...");
                
                $pendingTransactions = $user->creditTransactions()
                    ->where('payment_status', 'pending')
                    ->get();
                
                foreach ($pendingTransactions as $transaction) {
                    $this->line("Fixing transaction ID: {$transaction->id}");
                    
                    // Update transaction status
                    $transaction->update([
                        'payment_status' => 'completed',
                        'processed_at' => now(),
                    ]);
                    
                    // Add credits to balance
                    $user->increment('credit_balance', $transaction->credit_amount);
                    
                    $this->line("  ✅ Added {$transaction->credit_amount} credits");
                }
                
                $user->refresh();
                $this->info("New balance after fixing: {$user->credit_balance}");
            }
            
            // Verify balance calculation
            $this->info("\nBalance Verification:");
            
            $totalPurchased = $user->creditTransactions()
                ->where('type', 'purchase')
                ->where('payment_status', 'completed')
                ->sum('credit_amount');
            
            $totalUsed = abs($user->creditTransactions()
                ->where('type', 'usage')
                ->sum('credit_amount'));
            
            $totalCredits = $user->creditTransactions()
                ->where('type', 'credit')
                ->where('payment_status', 'completed')
                ->sum('credit_amount');
            
            $expectedBalance = $totalPurchased + $totalCredits - $totalUsed;
            
            $this->info("Total purchased (completed): {$totalPurchased}");
            $this->info("Total used: {$totalUsed}");
            $this->info("Total credits: {$totalCredits}");
            $this->info("Expected balance: {$expectedBalance}");
            $this->info("Actual balance: {$user->credit_balance}");
            
            if ($expectedBalance == $user->credit_balance) {
                $this->info("✅ Balance is correct!");
            } else {
                $this->warn("❌ Balance mismatch detected!");
                $this->info("Correcting balance...");
                $user->update(['credit_balance' => $expectedBalance]);
                $this->info("✅ Balance corrected to: {$expectedBalance}");
            }
            
        } else {
            $this->warn("No transactions found for this user");
        }
    }
    
    private function fixAllBalances()
    {
        $this->info("Fixing all user credit balances...");
        
        $usersWithTransactions = User::whereHas('creditTransactions')->get();
        
        $this->info("Found " . $usersWithTransactions->count() . " users with transactions");
        
        foreach ($usersWithTransactions as $user) {
            $this->fixUserBalance($user->email);
            $this->line("---");
        }
        
        $this->info("All balances fixed!");
    }
}
