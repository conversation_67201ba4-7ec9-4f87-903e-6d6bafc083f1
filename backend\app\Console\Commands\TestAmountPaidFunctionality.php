<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\CreditTransactionService;
use Illuminate\Console\Command;

class TestAmountPaidFunctionality extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'credit:test-amount-paid {--user-email=<EMAIL>}';

    /**
     * The console command description.
     */
    protected $description = 'Test amount_paid functionality with 1:1 credit deduction';

    protected CreditTransactionService $transactionService;

    public function __construct(CreditTransactionService $transactionService)
    {
        parent::__construct();
        $this->transactionService = $transactionService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userEmail = $this->option('user-email');
        $user = User::where('email', $userEmail)->first();

        if (!$user) {
            $this->error("User with email {$userEmail} not found.");
            return 1;
        }

        $this->info("Testing amount_paid functionality for user: {$user->name} ({$user->email})");
        $this->info("Initial balance: {$user->credit_balance} credits");
        $this->newLine();

        $initialBalance = $user->credit_balance;
        $testResults = [];

        // Test 1: Purchase with amount_paid
        $this->info("💰 Test 1: Purchase transaction with amount_paid...");
        try {
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'purchase',
                'credit_amount' => 100,
                'amount_paid' => 25.00, // Should deduct 25 credits
                'payment_method' => 'manual',
                'payment_status' => 'completed',
                'description' => 'Test purchase with amount_paid',
            ]);
            
            $user->refresh();
            $expectedBalance = $initialBalance + 100 - 25; // +100 credits, -25 for payment
            
            $this->info("✅ Transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: +{$transaction->credit_amount}");
            $this->info("   Amount paid: RM {$transaction->amount_paid} (should deduct 25 credits)");
            $this->info("   Expected balance: {$expectedBalance} credits");
            $this->info("   Actual balance: {$user->credit_balance} credits");
            
            if ($user->credit_balance == $expectedBalance) {
                $this->info("   ✅ Balance calculation correct!");
                $testResults['purchase_with_amount_paid'] = true;
            } else {
                $this->error("   ❌ Balance calculation incorrect!");
                $testResults['purchase_with_amount_paid'] = false;
            }
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $testResults['purchase_with_amount_paid'] = false;
        }
        $this->newLine();

        // Test 2: Purchase without amount_paid (should work as before)
        $this->info("💰 Test 2: Purchase transaction without amount_paid...");
        try {
            $balanceBeforeTest = $user->credit_balance;
            
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'purchase',
                'credit_amount' => 50,
                'payment_method' => 'manual',
                'payment_status' => 'completed',
                'description' => 'Test purchase without amount_paid',
            ]);
            
            $user->refresh();
            $expectedBalance = $balanceBeforeTest + 50; // Only +50 credits, no deduction
            
            $this->info("✅ Transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: +{$transaction->credit_amount}");
            $this->info("   Amount paid: " . ($transaction->amount_paid ?? 'null') . " (no deduction)");
            $this->info("   Expected balance: {$expectedBalance} credits");
            $this->info("   Actual balance: {$user->credit_balance} credits");
            
            if ($user->credit_balance == $expectedBalance) {
                $this->info("   ✅ Balance calculation correct!");
                $testResults['purchase_without_amount_paid'] = true;
            } else {
                $this->error("   ❌ Balance calculation incorrect!");
                $testResults['purchase_without_amount_paid'] = false;
            }
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $testResults['purchase_without_amount_paid'] = false;
        }
        $this->newLine();

        // Test 3: Refund with amount_paid
        $this->info("💸 Test 3: Refund transaction with amount_paid...");
        try {
            $balanceBeforeTest = $user->credit_balance;
            
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'refund',
                'credit_amount' => 30,
                'amount_paid' => 10.00, // Should deduct 10 credits
                'description' => 'Test refund with amount_paid',
            ]);
            
            $user->refresh();
            $expectedBalance = $balanceBeforeTest + 30 - 10; // +30 credits, -10 for payment
            
            $this->info("✅ Transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: +{$transaction->credit_amount}");
            $this->info("   Amount paid: RM {$transaction->amount_paid} (should deduct 10 credits)");
            $this->info("   Expected balance: {$expectedBalance} credits");
            $this->info("   Actual balance: {$user->credit_balance} credits");
            
            if ($user->credit_balance == $expectedBalance) {
                $this->info("   ✅ Balance calculation correct!");
                $testResults['refund_with_amount_paid'] = true;
            } else {
                $this->error("   ❌ Balance calculation incorrect!");
                $testResults['refund_with_amount_paid'] = false;
            }
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $testResults['refund_with_amount_paid'] = false;
        }
        $this->newLine();

        // Test 4: Usage transaction (should not have amount_paid)
        $this->info("📱 Test 4: Usage transaction (should not have amount_paid)...");
        try {
            $balanceBeforeTest = $user->credit_balance;
            
            $transaction = $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'usage',
                'credit_amount' => 20,
                'description' => 'Test usage transaction',
            ]);
            
            $user->refresh();
            $expectedBalance = $balanceBeforeTest - 20; // Only -20 credits
            
            $this->info("✅ Transaction created (ID: {$transaction->id})");
            $this->info("   Credit amount: {$transaction->credit_amount}");
            $this->info("   Amount paid: " . ($transaction->amount_paid ?? 'null') . " (should be null)");
            $this->info("   Expected balance: {$expectedBalance} credits");
            $this->info("   Actual balance: {$user->credit_balance} credits");
            
            if ($user->credit_balance == $expectedBalance && $transaction->amount_paid === null) {
                $this->info("   ✅ Balance calculation correct!");
                $testResults['usage_transaction'] = true;
            } else {
                $this->error("   ❌ Balance calculation incorrect!");
                $testResults['usage_transaction'] = false;
            }
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $testResults['usage_transaction'] = false;
        }
        $this->newLine();

        // Test 5: Balance verification for new transactions only
        $this->info("🧮 Test 5: Balance verification for new transactions...");

        // Calculate expected balance change from our test transactions
        $expectedChange = 100 - 25 + 50 + 30 - 10 - 20; // Test transactions: +100-25, +50, +30-10, -20
        $actualChange = $user->credit_balance - $initialBalance;

        if ($expectedChange == $actualChange) {
            $this->info("✅ Balance verification passed for new transactions");
            $this->info("   Expected change: {$expectedChange} credits");
            $this->info("   Actual change: {$actualChange} credits");
            $testResults['balance_verification'] = true;
        } else {
            $this->error("❌ Balance verification failed for new transactions");
            $this->error("   Expected change: {$expectedChange} credits");
            $this->error("   Actual change: {$actualChange} credits");
            $testResults['balance_verification'] = false;
        }

        $this->info("   Note: Full balance verification may differ due to existing transactions");
        $this->newLine();

        // Test 6: Insufficient credits validation
        $this->info("⚠️ Test 6: Insufficient credits validation...");
        try {
            // Try to create a transaction that would require more credits than available
            $this->transactionService->createTransaction([
                'user_id' => $user->id,
                'type' => 'purchase',
                'credit_amount' => 10,
                'amount_paid' => ($user->credit_balance + 100), // More than user has
                'payment_method' => 'manual',
                'payment_status' => 'completed',
                'description' => 'Test insufficient credits',
            ]);
            
            $this->error("   ❌ Should have failed due to insufficient credits!");
            $testResults['insufficient_credits_validation'] = false;
        } catch (\Exception $e) {
            $this->info("   ✅ Correctly prevented transaction: " . $e->getMessage());
            $testResults['insufficient_credits_validation'] = true;
        }
        $this->newLine();

        // Summary
        $this->info("📊 TEST SUMMARY:");
        $this->line(str_repeat("=", 60));
        
        $passedTests = 0;
        $totalTests = count($testResults);
        
        foreach ($testResults as $test => $passed) {
            $status = $passed ? "✅ PASS" : "❌ FAIL";
            $this->line(sprintf("%-35s: %s", ucfirst(str_replace('_', ' ', $test)), $status));
            if ($passed) $passedTests++;
        }
        
        $this->newLine();
        $this->info("Final balance: {$user->credit_balance} credits (started with {$initialBalance})");
        $this->info("Balance change: " . ($user->credit_balance - $initialBalance) . " credits");
        
        if ($passedTests == $totalTests) {
            $this->info("🎉 ALL TESTS PASSED! ({$passedTests}/{$totalTests})");
            $this->info("✅ Amount Paid functionality working correctly with 1:1 conversion rate");
            return 0;
        } else {
            $this->error("❌ SOME TESTS FAILED ({$passedTests}/{$totalTests} passed)");
            return 1;
        }
    }
}
